{"breadcrumb": {"Audit Logs": "<PERSON><PERSON>", "AutoFiling Settings": "AutoFiling Settings", "Client Branding": "Client Branding", "DMS Settings": "DMS Settings", "Dashboard Settings": "Dashboard Settings", "Email Format Settings": "Email Format Settings", "Folder Permissions": "Folder Permissions", "Portal Role Assignment": "Portal Role Assignment", "Portal Settings": "Portal Settings", "Privileges Settings": "Privileges Settings", "Shared Settings": "Shared Settings", "Staff Settings": "Staff Settings", "Terms & Conditions": "Terms & Conditions", "dms": "DMS", "portal": "DMS", "shared": "Shared", "dashboard": "DMS & Portal Manager"}, "btn": {"generatePreview": "Generate Preview", "auditTrails": "Audit Trails", "agree": "Agree & Continue", "cancel": "Cancel", "confirm": "Confirm"}, "section": {"uploadTitle": "Terms & Conditions Upload"}, "alert": {"message": "Please upload T&C File Below | Supported Formats: PDF | Max: 10MB"}, "upload": {"label": "File", "placeholder": "No file selected", "helperText": "Drag and drop your file here, or click the Browse button to select a file."}, "conditions": {"title": "Display Conditions", "subtext": "* Please select applicable Display Conditions", "firstLogin": "First Login", "updated": "When T&C are updated", "annually": "Annually", "quarterly": "Quarterly"}, "statement": {"title": "Statement of Agreement", "placeholder": "By selecting 'Agree & Continue', you acknowledge and confirm that you have read, understood and accept the Terms & Conditions set out above", "helper": "*Please edit the above text to update the 'Statement of Agreement' in the preview."}, "preview": {"title": "Terms & Condition Preview", "text": "By selecting 'Agree & Continue', you acknowledge and confirm that you have read, understand and accept the Terms & Conditions set out above.", "refresh": "Refresh", "download": "Download", "print": "Print"}, "reports": {"title": "Reports"}, "settings": {"title": "Settings"}, "error": {"title": "Error"}, "logout": "Logout", "darkMood": "Dark Mode", "dev": "Dev Components"}