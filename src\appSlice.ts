import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "@/store/store";

export type ThemeMode = "light" | "dark";

export interface AppState {
  themeMode: ThemeMode;
}

const initialState: AppState = {
  themeMode: "light",
};

export const appSlice = createSlice({
  name: "app",
  initialState,
  reducers: {
    toggleThemeMode: (state) => {
      const newMode = state.themeMode === "light" ? "dark" : "light";
      state.themeMode = newMode;
    },

    setThemeMode: (state, action: PayloadAction<ThemeMode>) => {
      state.themeMode = action.payload;
    },
  },
});

export const { toggleThemeMode, setThemeMode } = appSlice.actions;
export const selectThemeMode = (state: RootState) => state.app.themeMode;

export default appSlice.reducer;
