/**
 * Extracts and trims input value when the Enter key is pressed.
 * @param event - React keyboard event from input element.
 * @returns trimmed string if Enter key pressed, otherwise undefined
 */
function getInputOnEnter(
  event: React.KeyboardEvent<HTMLInputElement>,
): string | undefined {
  const isEnterKey = event.key === "Enter" || event.keyCode === 13;
  if (!isEnterKey) return;

  const target = event.target as HTMLInputElement;
  const value = target.value?.trim();

  return value || undefined;
}

export { getInputOnEnter };
