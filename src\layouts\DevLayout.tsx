import { Outlet } from "react-router-dom";
import Sidebar from "@/components/dashboardLayout/Sidebar/Sidebar";
import TopBar from "@/components/dashboardLayout/TopBar/TopBar";
import Footer from "@/components/dashboardLayout/Footer/Footer";
import styles from "@/layouts/DashboardLayout.module.css";

export default function DashboardLayout() {
  return (
    <div className={styles.dashboardContainer}>
      <TopBar />

      <div className={styles.dashboardMain}>
        <Sidebar />
        <div className={styles.dashboardContent}>
          <Outlet />
        </div>
      </div>

      <Footer />
    </div>
  );
}
