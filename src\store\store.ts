import { configureStore } from "@reduxjs/toolkit";
import type { ThunkAction, Action } from "@reduxjs/toolkit";
import appReducer from "@/appSlice";
import { todoApiSlice } from "@/api/todoApiSlice";
import { termsApiSlice } from "@/api/termsApiSlice";

// add slices here
export const store = configureStore({
  reducer: {
    app: appReducer,
    [todoApiSlice.reducerPath]: todoApiSlice.reducer,
    [termsApiSlice.reducerPath]: termsApiSlice.reducer,
    // Add other slices here as needed
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
      .concat(todoApiSlice.middleware)
      .concat(termsApiSlice.middleware),
  // add RTK Query middleware
});

// strong typing
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// async thunk actions
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;
