import { render, screen, fireEvent } from "@testing-library/react";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import Sidebar from "./Sidebar";

const renderSidebar = (collapsed = false) =>
  render(
    <MemoryRouter initialEntries={["/"]}>
      <Routes>
        <Route path="*" element={<Sidebar collapsed={collapsed} />} />
      </Routes>
    </MemoryRouter>,
  );

describe("Sidebar Component", () => {
  it("renders all top-level menu sections", () => {
    renderSidebar();

    expect(screen.getByText("Portal Settings")).toBeInTheDocument();
    expect(screen.getByText("DMS Settings")).toBeInTheDocument();
    expect(screen.getByText("Shared Settings")).toBeInTheDocument();
  });

  it("renders search input and updates search term on typing", () => {
    renderSidebar();

    const searchInput = screen.getByPlaceholderText("Search");
    fireEvent.change(searchInput, { target: { value: "Terms" } });

    expect(searchInput).toHaveValue("Terms");
  });

  it("filters menu and shows matched submenu items", () => {
    renderSidebar();

    fireEvent.change(screen.getByPlaceholderText("Search"), {
      target: { value: "audit" },
    });

    expect(screen.queryByText("Portal Settings")).not.toBeInTheDocument();
    expect(screen.queryByText("DMS Settings")).not.toBeInTheDocument();
    expect(screen.getByText("Shared Settings")).toBeInTheDocument();

    fireEvent.click(screen.getByText("Shared Settings"));
    expect(screen.getByText("Audit Logs")).toBeInTheDocument();
  });

  it("expands menu and shows submenu items when clicked", () => {
    renderSidebar();

    const portalMenu = screen.getByText("Portal Settings");
    fireEvent.click(portalMenu);

    expect(screen.getByText("Terms & Conditions")).toBeVisible();
    expect(screen.getByText("Portal Role Assignment")).toBeVisible();
  });

  it("triggers navigation when submenu item is clicked", () => {
    renderSidebar();

    fireEvent.click(screen.getByText("DMS Settings"));
    fireEvent.click(screen.getByText("Staff Settings"));

    expect(screen.getByText("Staff Settings")).toBeInTheDocument();
  });
});
