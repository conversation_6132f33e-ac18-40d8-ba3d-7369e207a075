import { DropDownList } from "@progress/kendo-react-dropdowns";
import type { DropDownListChangeEvent } from "@progress/kendo-react-dropdowns";
import { Icon } from "@progress/kendo-react-common";
import { useTranslation } from "react-i18next";
import { useLanguageSwitcher } from "./useLanguageSwitcher";
import styles from "./TopBar.module.scss";

const languages = [
  { lang: "en", nativeName: "EN" },
  { lang: "es", nativeName: "ES" },
];

export default function LanguageSwitcher() {
  const { t } = useTranslation();
  const { language, changeLanguage } = useLanguageSwitcher();

  const handleLangChange = (e: DropDownListChangeEvent) => {
    const selectedLang = e.value.lang;
    changeLanguage(selectedLang);
  };

  return (
    <div className={styles["topbar__item"]}>
      <Icon name="globe" style={{ width: 15, height: 15 }} />
      <DropDownList
        data={languages}
        textField="nativeName"
        value={languages.find((l) => l.lang === language)}
        onChange={handleLangChange}
        style={{ width: 80 }}
        aria-label={t("language")}
      />
    </div>
  );
}
