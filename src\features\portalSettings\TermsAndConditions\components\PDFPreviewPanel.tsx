import { PDFViewer } from "@progress/kendo-react-pdf-viewer";
import { Button } from "@progress/kendo-react-buttons";
import {
  ArrowDownTrayIcon,
  ArrowPathIcon,
  PrinterIcon,
} from "@heroicons/react/24/outline";
import { useTranslation } from "react-i18next";

interface Props {
  isSaving: boolean;
  onSave: () => void;
  file: File | null;
}

export default function PDFPreviewPanel({ isSaving, onSave }: Props) {
  const { t } = useTranslation("dashboard");

  return (
    <div className="pdf-preview-panel">
      <div className="pdf-viewer">
        <PDFViewer
          style={{
            width: "100%",
            height: "300px",
            border: "1px solid #e0e0e0",
          }}
        />
      </div>
      <div className="preview-text">{t("preview.text")}</div>
      <div className="preview-footer">
        <div className="preview-links">
          <a href="#" className="preview-link">
            <ArrowPathIcon />
            <span>{t("preview.refresh")}</span>
          </a>
          <a href="#" className="preview-link">
            <ArrowDownTrayIcon />
            <span>{t("preview.download")}</span>
          </a>
          <a href="#" className="preview-link">
            <PrinterIcon />
            <span>{t("preview.print")}</span>
          </a>
        </div>
        <Button
          themeColor="base"
          size="small"
          disabled={isSaving}
          onClick={onSave}
        >
          {isSaving ? t("btn.saving") : t("btn.agree")}
        </Button>
      </div>
    </div>
  );
}
