import { useState, useMemo } from "react";
import { menuData } from "@/constants/menuData";

export function useSidebar(searchTerm: string) {
  const [expandedIds, setExpandedIds] = useState<string[]>([]);

  const filteredMenu = useMemo(() => {
    return menuData
      .map((menu) => ({
        ...menu,
        children: menu.children.filter((child) =>
          child.title.toLowerCase().includes(searchTerm.toLowerCase()),
        ),
      }))
      .filter((menu) => menu.children.length > 0);
  }, [searchTerm]);

  const toggleExpanded = (id: string) => {
    setExpandedIds((prev) =>
      prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id],
    );
  };

  return { filteredMenu, expandedIds, toggleExpanded };
}
