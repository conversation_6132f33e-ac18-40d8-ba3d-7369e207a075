import "@progress/kendo-font-icons/dist/index.css"; 
import "@/styles/kendo-theme/scss/index.scss";

import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";

import { Provider } from "react-redux";
import { store } from "./store/store";

import { Security } from "@okta/okta-react";
import { OktaAuth } from "@okta/okta-auth-js";
import oktaConfig from "./oktaConfig";
import "@/utils/i18n";

const oktaAuth = new OktaAuth({
  issuer: oktaConfig.issuer,
  clientId: oktaConfig.clientId,
  redirectUri: oktaConfig.redirectUri,
  scopes: oktaConfig.scopes,
  pkce: oktaConfig.pkce,
  tokenManager: {
    storage: "localStorage",
  },
});

// Ensure Okta SDK initializes before rendering the app
(async () => {
  await oktaAuth.start();

  ReactDOM.createRoot(document.getElementById("root")!).render(
    <React.StrictMode>
      <Provider store={store}>
        <Security
          oktaAuth={oktaAuth}
          restoreOriginalUri={async (_oktaAuth, originalUri) => {
            window.location.replace(originalUri || "/dashboard");
          }}
        >
          <App />
        </Security>
      </Provider>
    </React.StrictMode>,
  );
})();
