import { useEffect, useState } from "react";
import i18n from "@/utils/i18n";

export const useLanguageSwitcher = () => {
  const [language, setLanguage] = useState(i18n.language);

  useEffect(() => {
    const savedLang = localStorage.getItem("language");
    if (savedLang && i18n.language !== savedLang) {
      i18n.changeLanguage(savedLang);
      setLanguage(savedLang);
    }

    const onLangChange = (lang: string) => setLanguage(lang);
    i18n.on("languageChanged", onLangChange);
    return () => i18n.off("languageChanged", onLangChange);
  }, []);

  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
    localStorage.setItem("language", lang);
  };

  return {
    language,
    changeLanguage,
  };
};
