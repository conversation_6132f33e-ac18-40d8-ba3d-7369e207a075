/* Layout */
.content-left,
.content-right {
  display: flex;
  flex-direction: column;
  flex: 1 1 500px;
  min-width: 300px;
  gap: 8px;
  height: 100%;
}

.section-header {
  margin: 0;
  background-color: var(--kendo-component-bg, #f5f6fa);
  padding: 5px 15px;
  border-bottom: 1px solid var(--kendo-border-color, #e0e0e0);
  font-weight: 600;
  font-size: var(--kendo-font-size-sm, 12px);
}

.section-inner {
  flex-grow: 1;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Header Action Buttons */
.header-action-btn {
  height: var(--kendo-button-height-sm, 1.75rem);
  font-size: var(--kendo-font-size-sm, 12px);
}

/* Alert Box */
.alert-box {
  display: flex;
  align-items: center;
  border: 1px solid var(--kendo-border-color, #ccc);
  background-color: var(--kendo-component-bg, #e6f4ff);
  padding: 6px;
  gap: 12px;
  border-radius: 2px;
}

.alert-icon {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  border: 1px solid var(--kendo-body-text, #333);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  line-height: 1;
}

.alert-message {
  font-size: var(--kendo-font-size-sm, 12px);
  flex-grow: 1;
}

/* Upload Section */
.upload-wrapper {
  padding: 24px;
  border: 1px solid var(--kendo-border-color, #e0e0e0);
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.upload-label {
  font-weight: bold;
  font-size: var(--kendo-font-size-sm, 12px);
}

.upload-input-row {
  display: flex;
  align-items: center;
  gap: 4px;
}

.upload-helper-text {
  font-size: var(--kendo-font-size-sm, 12px);
  color: var(--kendo-subtle-text, #666);
}

/* Conditions Form */
.conditions-wrapper {
  padding: 16px;
  border: 1px solid var(--kendo-border-color, #e0e0e0);
}

.conditions-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.conditions-header {
  display: flex;
  justify-content: space-between;
  font-size: var(--kendo-font-size-sm, 12px);
  font-weight: bold;
}

.conditions-subtext {
  font-size: var(--kendo-font-size-sm, 12px);
  color: var(--kendo-subtle-text, #666);
}

.conditions-options {
  display: flex;
  gap: 32px;
  flex-wrap: wrap;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: var(--kendo-font-size-sm, 12px);
}

.option-label {
  font-size: var(--kendo-font-size-sm, 12px);
}

/* Statement Editor */
.statement-editor {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.statement-wrapper {
  font-weight: bold;
  font-size: var(--kendo-font-size-sm, 12px);
}

.statement-card {
  padding: 16px;
  border: 1px solid var(--kendo-border-color, #e0e0e0);
}

.statement-helper {
  font-size: 13px;
  color: var(--kendo-subtle-text, #666);
}

/* PDF Preview Panel */
.pdf-preview-panel {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-grow: 1;
}

.pdf-viewer {
  width: 100%;
  height: 100%;
  flex-grow: 1;
  border: 1px solid var(--kendo-border-color, #e0e0e0);
}

.preview-text {
  font-size: 13px;
  color: var(--kendo-body-text, #333);
  padding: 0 2px;
  border-top: 1px solid var(--kendo-border-color, #e0e0e0);
  border-bottom: 1px solid var(--kendo-border-color, #e0e0e0);
}

.preview-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 2px;
}

.preview-links {
  display: flex;
  gap: 16px;
  align-items: center;
}

.preview-link {
  display: flex;
  align-items: center;
  font-size: var(--kendo-font-size-sm, 12px);
  text-decoration: none;
  color: var(--kendo-body-text, #333);

  svg {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
}

/* Cancel Link */
.cancel-link {
  text-decoration: underline;
  color: var(--kendo-link-text, #0078d4);
  font-size: var(--kendo-font-size-md, 14px);
}

/* Responsive Adjustments */
@media (max-height: 800px) {
  .dashboard-main {
    gap: 8px;
    margin-bottom: 8px;
  }

  .upload-wrapper,
  .conditions-wrapper,
  .statement-card {
    padding: 8px;
  }

  .section-inner {
    padding: 4px;
    gap: 4px;
  }

  .section-header {
    padding: 4px 10px;
    font-size: calc(var(--kendo-font-size-sm, 12px) - 1px);
  }

  .alert-message,
  .upload-label,
  .option-label,
  .statement-wrapper,
  .statement-helper,
  .preview-text,
  .preview-link span {
    font-size: calc(var(--kendo-font-size-sm, 12px) - 1px);
  }

  .preview-footer {
    padding: 2px 0;
  }

  .cancel-link {
    font-size: calc(var(--kendo-font-size-sm, 12px) - 1px);
  }
}