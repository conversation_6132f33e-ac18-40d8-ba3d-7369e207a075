import AlertBox from "@/components/ui/AlertBox/AlertBox";
import "./LandingPage.scss";
import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { useTranslation } from "react-i18next";
import { useDashboard } from "./hooks/useDashboard";

export default function LandingPage() {
  const { t } = useTranslation("dashboard");

  const { showAlert, setShowAlert } = useDashboard();

  return (
    <SectionLayout
      // headerActions={
      //   <>
      //     <div className="landing-page-header"></div>
      //   </>
      // }
      // footer={
      //   <>
      //     <div className="landing-page-footer"></div>
      //   </>
      // }
    >
      <div className="landing-page-content">
        {showAlert && (
          <AlertBox
            message={"Please select a settings area in the navigation panel to the left, or via the recent activity panel below."}
            onClose={() => setShowAlert(false)}
          />
        )}
      </div>
    </SectionLayout>
  );
}
