import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@progress/kendo-react-buttons";
import { GridLayout, GridLayoutItem } from "@progress/kendo-react-layout";

import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import AlertBox from "@/components/ui/AlertBox/AlertBox";
import FormCard from "@/components/ui/FormCard/FormCard";

import UploadSection from "./components/UploadSection";
import ConditionsForm from "./components/ConditionsForm";
import StatementEditor from "./components/StatementEditor";
import PDFPreviewPanel from "./components/PDFPreviewPanel";

import { useTermsAndConditionsController } from "./hooks/useTermsAndConditionsController";

import "./TermsAndConditions.scss";
import { ROUTES } from "@/constants/routes";

export default function TermsAndConditionsPage() {
  const { t } = useTranslation("dashboard");

  const {
    isLoading,
    isFetching,
    isSaving,
    errorMessage,
    setErrorMessage,
    showAlert,
    setShowAlert,
    uploadedFileName,
    selectedFile,
    displayCondition,
    setDisplayCondition,
    handleUpload,
    handleRemoveFile,
    handleSaveTerms,
    navigate,
  } = useTermsAndConditionsController();

  return (
    <SectionLayout
      isLoading={isLoading}
      isFetching={isFetching}
      isSaving={isSaving}
      errorMessage={errorMessage}
      onCloseError={() => setErrorMessage("")}
      headerActions={
        <>
          <Button
            size="small"
            className="header-action-btn"
            icon="file"
            themeColor="base"
          >
            {t("btn.generatePreview")}
          </Button>
          <Button
            size="small"
            className="header-action-btn"
            icon="check"
            themeColor="base"
            onClick={() => navigate(ROUTES.AUDIT_TRAILS)}
          >
            {t("btn.auditTrails")}
          </Button>
        </>
      }
      footer={
        <>
          <a href="#" className="cancel-link">
            {t("btn.cancel")}
          </a>
          <Button
            themeColor="base"
            disabled={isSaving}
            onClick={handleSaveTerms}
          >
            {isSaving ? t("btn.saving") : t("btn.confirm")}
          </Button>
        </>
      }
    >
      <GridLayout
        cols={[{ width: "1fr" }, { width: "1fr" }]}
        gap={{ rows: 16, cols: 24 }}
        style={{ width: "100%" }}
      >
        {/* Left Side */}
        <GridLayoutItem>
          <FormCard title={t("section.uploadTitle")} className="content-left">
            {showAlert && (
              <AlertBox
                message={t("alert.message")}
                onClose={() => setShowAlert(false)}
              />
            )}

            <FormCard className="upload-wrapper">
              <UploadSection
                uploadedFileName={uploadedFileName}
                onUpload={handleUpload}
                onRemove={handleRemoveFile}
              />
            </FormCard>

            <FormCard className="conditions-wrapper">
              <ConditionsForm
                displayCondition={displayCondition}
                onChange={(e) => setDisplayCondition(e.value)}
              />
            </FormCard>

            <StatementEditor />
          </FormCard>
        </GridLayoutItem>

        {/* Right Side */}
        <GridLayoutItem>
          <FormCard title={t("preview.title")} className="content-right">
            <PDFPreviewPanel
              isSaving={isSaving}
              onSave={handleSaveTerms}
              file={selectedFile}
            />
          </FormCard>
        </GridLayoutItem>
      </GridLayout>
    </SectionLayout>
  );
}
