/* eslint-disable no-console */
// Console allowed here for centralized logger output
import type { ILogger, LogLevel } from "./ILogger";

const LOG_LEVEL_PRIORITY: Record<LogLevel, number> = {
  debug: 1,
  info: 2,
  warn: 3,
  error: 4,
};

const CURRENT_LOG_LEVEL: LogLevel =
  (import.meta.env.VITE_LOG_LEVEL as LogLevel) || "warn";

const shouldLog = (level: LogLevel) => {
  return LOG_LEVEL_PRIORITY[level] >= LOG_LEVEL_PRIORITY[CURRENT_LOG_LEVEL];
};

const formatMessage = (
  level: LogLevel,
  message: string,
  context?: Record<string, any>,
) => {
  const timestamp = new Date().toISOString();
  const contextStr = context ? ` | Context: ${JSON.stringify(context)}` : "";
  return `[${timestamp}] [${level.toUpperCase()}] ${message}${contextStr}`;
};

const logger: ILogger = {
  debug: (message, context) => {
    if (shouldLog("debug"))
      console.debug(formatMessage("debug", message, context));
  },
  info: (message, context) => {
    if (shouldLog("info"))
      console.info(formatMessage("info", message, context));
  },
  warn: (message, context) => {
    if (shouldLog("warn"))
      console.warn(formatMessage("warn", message, context));
  },
  error: (message, context) => {
    if (shouldLog("error"))
      console.error(formatMessage("error", message, context));
  },
};

export default logger;
