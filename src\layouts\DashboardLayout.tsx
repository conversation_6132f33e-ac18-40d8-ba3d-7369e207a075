import { useState } from "react";
import { Splitter } from "@progress/kendo-react-layout";
import type {
  SplitterOnChangeEvent,
  SplitterPaneProps,
} from "@progress/kendo-react-layout";
import Sidebar from "@/components/dashboardLayout/Sidebar/Sidebar";
import TopBar from "@/components/dashboardLayout/TopBar/TopBar";
import Footer from "@/components/dashboardLayout/Footer/Footer";
import styles from "./DashboardLayout.module.css";
import { Outlet } from "react-router-dom";

export default function DashboardLayout() {
  const [collapsed, setCollapsed] = useState(false);
  const [sizes, setSizes] = useState<SplitterPaneProps[]>([
    {
      size: "255px",
      min: "60px",
      max: "50%",
      collapsible: true,
      collapsed: false,
    },
    {
      min: "300px",
    },
  ]);

  const handleSplitterChange = (event: SplitterOnChangeEvent) => {
    setSizes(event.newState);
    setCollapsed(event.newState[0].collapsed ?? false);
  };

  return (
    <div className={styles.dashboardContainer}>
      <TopBar />
      <div className={styles.topGap}></div>

      <div className={styles.dashboardMain}>
        <Splitter
          style={{ height: "100%" }}
          panes={sizes}
          onChange={handleSplitterChange}
        >
          <div className={styles.sidebar}>
            <Sidebar collapsed={collapsed} />
          </div>

          <div className={styles.dashboardContent}>
            <div className={styles.dashboardContentInner}>
              <Outlet />
            </div>
          </div>
        </Splitter>
      </div>

      <Footer />
    </div>
  );
}
