import { AppBar, AppBarSection } from "@progress/kendo-react-layout";
import styles from "./TopBar.module.scss";
import LanguageSwitcher from "./LanguageSwitcher";
import UserMenu from "./UserMenu";

export default function TopBar() {
  return (
    <AppBar positionMode="static" className={styles["topbar"]}>
      <AppBarSection>{/* title or logo */}</AppBarSection>

      <AppBarSection className={styles["topbar__actions"]}>
        <LanguageSwitcher />
        <UserMenu />
      </AppBarSection>
    </AppBar>
  );
}
