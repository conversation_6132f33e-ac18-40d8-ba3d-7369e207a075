import { createApi } from "@reduxjs/toolkit/query/react";
import type {
  FetchBaseQueryError,
  QueryReturnValue,
} from "@reduxjs/toolkit/query";
import type { Terms } from "@/types/terms";
import config from "@/config";
import { baseQueryWithReauth } from "./interceptorsSlice";
import { mockTermsData } from "./mocks/terms.mock";

export const termsApiSlice = createApi({
  reducerPath: "termsApi",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    // GET TERMS
    getTerms: builder.query<Terms, void>({
      async queryFn(
        _arg,
        _queryApi,
        _extraOptions,
        baseQuery,
      ): Promise<QueryReturnValue<Terms, FetchBaseQueryError, {} | undefined>> {
        if (config.featureFlags.TERMS_API_USE_MOCK) {
          return {
            data: mockTermsData,
          };
        }

        const result = await baseQuery({
          url: "/terms-and-conditions",
          method: "GET",
        });

        return result as QueryReturnValue<
          Terms,
          FetchBaseQueryError,
          {} | undefined
        >;
      },
    }),

    // SAVE TERMS
    saveTerms: builder.mutation<void, Partial<Terms>>({
      async queryFn(
        body,
        _queryApi,
        _extraOptions,
        baseQuery,
      ): Promise<QueryReturnValue<void, FetchBaseQueryError, {} | undefined>> {
        if (config.featureFlags.TERMS_API_USE_MOCK) {
          return { data: undefined }; // simulate success
        }

        const result = await baseQuery({
          url: "/terms-and-conditions",
          method: "POST",
          body,
        });

        return result as QueryReturnValue<
          void,
          FetchBaseQueryError,
          {} | undefined
        >;
      },
    }),
  }),
});

export const { useGetTermsQuery, useSaveTermsMutation } = termsApiSlice;
