import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import TopBar from "./TopBar";
import { vi } from "vitest";

// Mock setup
const logoutMock = vi.fn();
const changeLanguageMock = vi.fn();
const closePopupMock = vi.fn();

vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

vi.mock("@/hooks/useAuth", () => ({
  useAuth: () => ({
    user: { name: "TestUser" },
    logout: logoutMock,
  }),
}));

vi.mock("./useLanguageSwitcher", () => ({
  useLanguageSwitcher: () => ({
    language: "en",
    changeLanguage: changeLanguageMock,
  }),
}));

vi.mock("./useUserMenu", async () => {
  const actual = await vi.importActual("./useUserMenu");
  const ref = { current: document.createElement("div") };
  return {
    ...actual,
    useUserMenu: () => ({
      showPopup: true,
      userRef: ref,
      popupRef: ref,
      togglePopup: vi.fn(),
      closePopup: closePopupMock,
    }),
  };
});

// Tests
describe("TopBar", () => {
  beforeEach(() => {
    logoutMock.mockClear();
    changeLanguageMock.mockClear();
    closePopupMock.mockClear();
  });

  it("renders the username", () => {
    render(<TopBar />);
    expect(screen.getByText("TestUser")).toBeInTheDocument();
  });

  it("shows the popup when avatar is clicked", async () => {
    render(<TopBar />);
    const userButton = screen.getByRole("button", { name: /testuser/i });
    await userEvent.click(userButton);
    expect(screen.getByRole("menu")).toBeInTheDocument();
  });

  it("calls logout function when logout item is clicked", async () => {
    vi.unmock("./useUserMenu");
    render(<TopBar />);
    const userButton = screen.getByRole("button", { name: /testuser/i });
    await userEvent.click(userButton);
    const logoutBtn = await screen.findByRole("menuitem");
    await userEvent.click(logoutBtn);
    expect(logoutMock).toHaveBeenCalled();
  });

  it("closes the popup when Escape key is pressed", async () => {
    vi.unmock("./useUserMenu");
    render(<TopBar />);
    fireEvent.keyDown(document, { key: "Escape", code: "Escape" });
    await waitFor(() => {
      expect(screen.queryByRole("menu")).not.toBeInTheDocument();
    });
  });

  it("calls changeLanguage when a language is selected", async () => {
    render(<TopBar />);
    const dropdown = screen.getByRole("combobox");
    await userEvent.click(dropdown); // open the dropdown
    const spanishOption = await screen.findByText("ES");
    await userEvent.click(spanishOption);
    expect(changeLanguageMock).toHaveBeenCalledWith("es");
  });
});
