import { Card } from "@progress/kendo-react-layout";
import { TextArea } from "@progress/kendo-react-inputs";
import { useTranslation } from "react-i18next";

export default function StatementEditor() {
  const { t } = useTranslation("dashboard");

  return (
    <div className="statement-editor">
      <div className="statement-wrapper">
        {t("statement.title")} <span style={{ color: "red" }}>**</span>
      </div>
      <Card className="statement-card">
        <TextArea
          rows={2}
          value={t("statement.placeholder")}
          className="text-sm"
        />
      </Card>
      <div className="statement-helper">{t("statement.helper")}</div>
    </div>
  );
}
