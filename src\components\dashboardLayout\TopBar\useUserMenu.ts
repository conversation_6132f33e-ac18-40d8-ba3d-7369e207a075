import { useEffect, useRef, useState } from "react";

export const useUserMenu = () => {
  const [showPopup, setShowPopup] = useState(false);
  const userRef = useRef<HTMLButtonElement>(null);
  const popupRef = useRef<HTMLDivElement>(null);

  const togglePopup = () => setShowPopup((prev) => !prev);
  const closePopup = () => setShowPopup(false);

  useEffect(() => {
    const handleDocumentClick = (e: MouseEvent) => {
      if (
        userRef.current &&
        !userRef.current.contains(e.target as Node) &&
        popupRef.current &&
        !popupRef.current.contains(e.target as Node)
      ) {
        closePopup();
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        closePopup();
      }
    };

    document.addEventListener("click", handleDocumentClick);
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("click", handleDocumentClick);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  return {
    showPopup,
    userRef,
    popupRef,
    togglePopup,
    closePopup,
  };
};
