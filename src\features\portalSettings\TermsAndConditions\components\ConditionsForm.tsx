import { Checkbox, RadioButton } from "@progress/kendo-react-inputs";
import { useTranslation } from "react-i18next";

interface Props {
  displayCondition: string;
  onChange: (_e: any) => void;
}

export default function ConditionsForm({ displayCondition, onChange }: Props) {
  const { t } = useTranslation("dashboard");

  return (
    <div className="conditions-form">
      <div className="conditions-header">
        {t("conditions.title")}
        <span className="required">*</span>
      </div>
      <div className="conditions-subtext">{t("conditions.subtext")}</div>
      <div className="conditions-options">
        <Checkbox
          label={
            <span className="option-label">{t("conditions.firstLogin")}</span>
          }
        />
        <Checkbox
          label={
            <span className="option-label">{t("conditions.updated")}</span>
          }
        />
        <div className="radio-label">
          <RadioButton
            name="displayFrequency"
            value="Annually"
            checked={displayCondition === "Annually"}
            onChange={onChange}
            label=""
          />
          <span>{t("conditions.annually")}</span>
        </div>
        <div className="radio-label">
          <RadioButton
            name="displayFrequency"
            value="Quarterly"
            checked={displayCondition === "Quarterly"}
            onChange={onChange}
            label=""
          />
          <span>{t("conditions.quarterly")}</span>
        </div>
      </div>
    </div>
  );
}
