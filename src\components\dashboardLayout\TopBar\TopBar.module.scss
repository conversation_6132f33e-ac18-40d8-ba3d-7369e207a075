.topbar {
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;
  height: 35px;
  min-height: 35px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1.5rem;
  z-index: 1000;
  color: #212529;
  box-shadow: none !important;
}

.topbar__title {
  margin: 0;
  font-size: 1.2rem;
}

.topbar__actions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.topbar__item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 1rem;

  &:hover {
    opacity: 0.8;
  }
}

.topbar__item--user {
  position: relative;
  user-select: none;
}

.topbar__popup {
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0.25rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  z-index: 2000;
  min-width: 120px;
}

.topbar__popup-item {
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  font-size: 0.95rem;
  color: #212529;
  white-space: nowrap;
  cursor: pointer;

  &:hover {
    background-color: #f2f2f2;
  }
}

/* Responsive Tweaks */
@media (max-width: 768px) {
  .topbar {
    padding: 0 1rem;
  }

  .topbar__actions {
    gap: 1rem;
  }

  .topbar__item {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .topbar__title {
    display: none;
  }

  .topbar__item span {
    display: none;
  }

  .topbar__actions {
    gap: 0.5rem;
  }
}
