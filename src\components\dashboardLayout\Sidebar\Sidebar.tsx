import { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { PanelBar, PanelBarItem } from "@progress/kendo-react-layout";
import { SvgIcon } from "@progress/kendo-react-common";
import { caretAltUpIcon, caretAltDownIcon } from "@progress/kendo-svg-icons";

import SidebarSearch from "./SidebarSearch";
import { menuIconMap, childIconMap } from "@/constants/iconMaps";
import { useSidebar } from "./useSidebar";

import "./Sidebar.scss";

interface SidebarProps {
  collapsed: boolean;
}

export default function Sidebar({ collapsed }: SidebarProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const { filteredMenu, expandedIds, toggleExpanded } = useSidebar(searchTerm);
  const navigate = useNavigate();
  const location = useLocation();

  return (
    <div className={`sidebar-container ${collapsed ? "collapsed" : ""}`}>
      {!collapsed && (
        <>
          <SidebarSearch
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
          />
          <div className="sidebar-divider" />
        </>
      )}

      <div className="sidebar-menu-scroll">
        <PanelBar expandMode="multiple">
          {!collapsed
            ? filteredMenu.map((menu) => {
                const isExpanded = expandedIds.includes(menu.id);
                const MenuIcon = menuIconMap[menu.icon];

                return (
                  <PanelBarItem
                    key={menu.id}
                    expanded={isExpanded}
                    className="custom-panelbar-item"
                    title={
                      <div
                        className={`menu-title ${isExpanded ? "expanded" : ""}`}
                        onClick={() => toggleExpanded(menu.id)}
                      >
                        <SvgIcon
                          icon={isExpanded ? caretAltUpIcon : caretAltDownIcon}
                          className="caret-placeholder"
                        />
                        <MenuIcon className="menu-heroicon" />
                        <span className="menu-text">{menu.title}</span>
                        {!collapsed && (
                          <span className="menu-count">
                            ({menu.children.length})
                          </span>
                        )}
                      </div>
                    }
                  >
                    {menu.children.map((child) => {
                      const ChildIcon = childIconMap[child.icon];
                      return (
                        <PanelBarItem
                          key={child.route}
                          title={
                            <div
                              className={`submenu-item ${
                                location.pathname === child.route
                                  ? "active"
                                  : ""
                              }`}
                              onClick={() => navigate(child.route)}
                            >
                              <ChildIcon className="submenu-heroicon" />
                              <span>{child.title}</span>
                            </div>
                          }
                        />
                      );
                    })}
                  </PanelBarItem>
                );
              })
            : filteredMenu.map((menu) => {
                const Icon = menuIconMap[menu.icon];
                return (
                  <PanelBarItem
                    key={menu.id}
                    title={
                      <div
                        className="collapsed-icon-only"
                        onClick={() => toggleExpanded(menu.id)}
                      >
                        <Icon className="menu-icon" />
                      </div>
                    }
                  />
                );
              })}
        </PanelBar>
      </div>
    </div>
  );
}
