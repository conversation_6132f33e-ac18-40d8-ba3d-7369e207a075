import type { RouteObject } from "react-router-dom";
import { ROUTES } from "@/constants/routes";
import DevLayout from "@/layouts/DevLayout";
import React, { Suspense } from "react";

const DevKendoShowcasePage = React.lazy(
  () => import("@/features/dev/DevKendoShowcasePage"),
);

export const devRoutes: RouteObject[] = [
  {
    path: ROUTES.DEV,
    element: <DevLayout />,
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<div>DevKendoShowcasePage...</div>}>
            <DevKendoShowcasePage />
          </Suspense>
        ),
      },
    ],
  },
];
