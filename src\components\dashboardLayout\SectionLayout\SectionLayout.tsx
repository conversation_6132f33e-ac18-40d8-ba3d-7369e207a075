import type { ReactNode } from "react";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { Card } from "@progress/kendo-react-layout";
import { Loader } from "@progress/kendo-react-indicators";
import {
  Notification,
  NotificationGroup,
} from "@progress/kendo-react-notification";
import "./SectionLayout.scss";

import { menuData } from "@/constants/menuData";

interface SectionLayoutProps {
  isLoading?: boolean;
  isFetching?: boolean;
  isSaving?: boolean;
  errorMessage?: string;
  onCloseError?: () => void;
  headerActions?: ReactNode;
  children: ReactNode;
  footer?: ReactNode;
}

export default function SectionLayout({
  isLoading = false,
  isFetching = false,
  isSaving = false,
  errorMessage = "",
  onCloseError,
  headerActions,
  children,
  footer,
}: SectionLayoutProps) {
  const { t } = useTranslation("dashboard");
  const location = useLocation();

  const path = location.pathname;
  const isDashboard = path === "/dashboard";

  const matchedMenu = menuData.find((menu) =>
    menu.children.some((child) => child.route === path)
  );
  const matchedChild = matchedMenu?.children.find(
    (child) => child.route === path
  );

  const breadcrumbKeys = [
    "portal",
    matchedMenu?.title || "",
    matchedChild?.title || "",
  ].filter(Boolean);

  const showLoader = isLoading || isFetching || isSaving;

  return (
    <div className="dashboard-page">
      {showLoader && (
        <div className="loading-overlay">
          <Loader size="large" type="infinite-spinner" themeColor="primary" />
        </div>
      )}

      {errorMessage && (
        <NotificationGroup style={{ right: 10, top: 10 }}>
          <Notification
            type={{ style: "error", icon: true }}
            closable={true}
            onClose={onCloseError}
          >
            <span>{errorMessage}</span>
          </Notification>
        </NotificationGroup>
      )}

      <Card className="dashboard-header">
        <div className="header-content">
          <div className="breadcrumbs">
            {isDashboard
              ? <h3>{t("breadcrumb.dashboard")}</h3>
              : breadcrumbKeys.map((key, index) => (
                  <div key={key} className="breadcrumb-item">
                    {index !== 0 && (
                      <span className="breadcrumb-divider">/</span>
                    )}
                    <span>{t(`breadcrumb.${key}`)}</span>
                  </div>
                ))}
          </div>
          <div className="header-actions">{headerActions}</div>
        </div>
      </Card>

      <Card className="dashboard-content-card">
        <div className="dashboard-main">{children}</div>
        {footer && <div className="dashboard-footer">{footer}</div>}
      </Card>
    </div>
  );
}
