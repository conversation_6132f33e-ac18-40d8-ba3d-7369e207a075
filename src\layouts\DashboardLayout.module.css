.dashboardContainer {
  display: grid;
  grid-template-rows: auto 40px minmax(0, 1fr) 40px auto; /* Corrected here */
  height: 100vh;
  background-color: #f5f6fa;
}

.topGap {
  background-color: #f5f6fa;
  height: 16px;
}

.dashboardMain {
  margin-top: -40px;
  display: flex;
  flex-direction: row;
  align-items: stretch; /* Sidebar and Content same height */
  gap: 1rem; /* Clean gap between Sidebar and Content */
  background-color: #f5f6fa;
  padding: 1rem;
  height: 100%;
  min-height: 0;
}

.sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: transparent;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  overflow: visible;
}

.dashboardContent {
  flex: 1;
  overflow-y: auto; /* Scrollable content */
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
  padding: 0; /* No padding here */
  background: none;
  border: none;
  border-radius: 0;
  box-shadow: none;
}

/* Inner wrapper for Content padding and styling */
.dashboardContentInner {
  flex: 1 1 auto;
  min-height: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background: #f5f6fa;
  border-radius: 1px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  position: relative; 
}

.bottomGap {
  background-color: #f5f6fa;
}
