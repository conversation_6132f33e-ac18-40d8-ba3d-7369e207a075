import { useState, useMemo, useEffect, useCallback } from "react";
import { debounce } from "lodash";
import { useGetTerms } from "./useGetTerms";
import { useSaveTerms } from "./useSaveTerms";
import { useNavigate } from "react-router-dom";

export function useTermsAndConditionsController() {
  const { isLoading, isFetching } = useGetTerms();
  const { save, isSaving } = useSaveTerms();

  const [errorMessage, setErrorMessage] = useState("");
  const [showAlert, setShowAlert] = useState(true);
  const [uploadedFileName, setUploadedFileName] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [displayCondition, setDisplayCondition] = useState("");

  const handleSubmit = useCallback(() => {
    if (selectedFile) {
      // upload to server
    }
  }, [selectedFile]);

  const handleUpload = useMemo(
    () =>
      debounce((event: any) => {
        const file = event.affectedFiles[0];
        if (file) {
          setUploadedFileName(file.name);
          setSelectedFile(file);
          handleSubmit();
        }
      }, 300),
    [handleSubmit],
  );

  useEffect(() => {
    return () => {
      handleUpload.cancel();
    };
  }, [handleUpload]);

  const handleRemoveFile = () => {
    setUploadedFileName("");
    setSelectedFile(null);
  };

  const handleSaveTerms = async () => {
    const payload = {
      title: "Terms Title",
      content: "Terms content...",
      lastUpdated: new Date().toISOString(),
    };

    await save(payload, (message) => setErrorMessage(message));
  };

  const navigate = useNavigate();

  return {
    isLoading,
    isFetching,
    isSaving,
    errorMessage,
    setErrorMessage,
    showAlert,
    setShowAlert,
    uploadedFileName,
    setUploadedFileName,
    selectedFile,
    setSelectedFile,
    displayCondition,
    setDisplayCondition,
    handleUpload,
    handleRemoveFile,
    handleSaveTerms,
    navigate,
  };
}
