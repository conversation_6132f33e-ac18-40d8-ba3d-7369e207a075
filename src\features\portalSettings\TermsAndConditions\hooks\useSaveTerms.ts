import { useSaveTermsMutation } from "@/api/termsApiSlice";
import logger from "@/utils/logger";

export const useSaveTerms = () => {
  const [saveTerms, { isLoading }] = useSaveTermsMutation();

  const save = async (
    payload: any,
    onError: (_message: string) => void,
  ): Promise<void> => {
    try {
      await saveTerms(payload).unwrap();
    } catch (error: any) {
      logger.error("Save failed in useSaveTerms", error);
      const message = error?.data?.message || error?.message || "Save failed.";
      onError(message);
    }
  };

  return { save, isSaving: isLoading };
};
