// Compatible with @progress/kendo-theme-default v.11.0.2

@use 'tokens' as *;
  @use 'placeholders' as *;

  @use 'kendo' as *;

    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button{
    border-bottom-color: $tb-iris-primary-blue;
            border-left-color: $tb-iris-primary-blue;
            border-right-color: $tb-iris-primary-blue;
            border-top-color: $tb-iris-primary-blue;
            font-size: 14px;
            line-height: 1.12;
            padding-top: 8px;
            padding-right: 10px;
            padding-bottom: 8px;
            padding-left: 10px;
            font-weight: 400;
            background-color: $tb-kendo-button-bg;
            background-image: none;
            border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
            border-bottom-left-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-top-left-radius: $tb-iris-border-radius;
            border-top-right-radius: $tb-iris-border-radius;
            row-gap: $tb-iris-button-gap;
            column-gap: $tb-iris-button-gap;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
            height: 32px;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-disabled, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-state-disabled, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button:disabled{
    border-bottom-color: $tb-iris-neutral-grey-70;
            border-left-color: $tb-iris-neutral-grey-70;
            border-right-color: $tb-iris-neutral-grey-70;
            border-top-color: $tb-iris-neutral-grey-70;
            border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
            color: $tb-iris-neutral-grey-70;
            background-color: $tb-iris-neutral-grey-5;
            background-image: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02));
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button.k-hover, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button.k-state-hover, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button.k-state-hovered, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button:hover{
    border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
            border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
            background-color: $tb-iris-primary-blue-dark-30;
            background-image: none;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-hover, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-state-hover, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-state-hovered, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button:hover{
    border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
            border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
            color: $tb-iris-primary-blue-dark-30;
            background-color: $tb-kendo-button-bg;
            background-image: none;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-hover,.k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-state-hover,.k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-state-hovered,.k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button:hover{
    margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-hover{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
      
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-selected{
    color: $tb-iris-primary-blue-dark-30;
            background-color: $tb-kendo-button-bg;
            background-image: none;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
            border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-active, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-state-active, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button:active{
    border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
            color: $tb-iris-primary-blue-dark-30;
            background-color: $tb-kendo-button-bg;
            background-image: none;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-active,.k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-state-active,.k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button:active{
    margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-focus, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-state-focus, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-state-focused, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button:focus{
                    @extend %tb-effects-tb-internal-none-effects;
          color: $tb-iris-primary-blue-dark-30;
            background-color: $tb-kendo-button-bg;
            background-image: none;
            border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-focus,.k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-state-focus,.k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button.k-state-focused,.k-button-md.k-rounded-md.k-button-solid.k-button-solid-base.k-button:focus{
    margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button.k-focus, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button.k-state-focus, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button.k-state-focused, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button:focus{
    background-color: $tb-iris-primary-blue-dark-30;
            background-image: none;
            border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
            box-shadow: none;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button.k-selected{
    background-color: $tb-iris-primary-blue-dark-30;
            background-image: none;
            border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button.k-active, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button.k-state-active, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button:active{
    background-color: $tb-iris-primary-blue-dark-30;
            background-image: none;
            border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button.k-disabled, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button.k-state-disabled, .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button:disabled{
    background-color: $tb-iris-neutral-grey-5;
            background-image: none;
            color: $tb-iris-neutral-grey-70;
        
}
    .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-focus,.k-list.k-list-md .k-list-content .k-list-ul  .k-list-item.k-state-focus,.k-list.k-list-md .k-list-content .k-list-ul  .k-list-item.k-state-focused,.k-list.k-list-md .k-list-content .k-list-ul  .k-list-item:focus{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-disabled,.k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-state-disabled,.k-list.k-list-md .k-list-content .k-list-ul .k-list-item:disabled{
    background-color: $tb-disabled-background;
            background-image: none;
            color: $tb-iris-neutral-grey-70;
        
}
    .k-list-filter .k-searchbox.k-invalid.k-input-solid,.k-list-filter  .k-searchbox.ng-invalid.ng-touched.k-input-solid,.k-list-filter  .k-searchbox.ng-invalid.ng-dirty.k-input-solid{
    border-bottom-color: $tb-iris-primary-red;
            border-left-color: $tb-iris-primary-red;
            border-right-color: $tb-iris-primary-red;
            border-top-color: $tb-iris-primary-red;
        
}
    .k-list-filter .k-searchbox.k-invalid.k-input,.k-list-filter  .k-searchbox.ng-invalid.ng-touched.k-input,.k-list-filter  .k-searchbox.ng-invalid.ng-dirty.k-input{
    border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
        
}
    .k-list-filter .k-searchbox.k-textbox.k-focus.k-input-solid,.k-list-filter  .k-searchbox.k-textbox.k-state-focus.k-input-solid,.k-list-filter  .k-searchbox.k-textbox.k-state-focused.k-input-solid,.k-list-filter  .k-searchbox.k-textbox:focus.k-input-solid{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-list-filter .k-searchbox.k-hover.k-input-solid,.k-list-filter  .k-searchbox.k-state-hover.k-input-solid,.k-list-filter  .k-searchbox.k-state-hovered.k-input-solid,.k-list-filter  .k-searchbox:hover.k-input-solid{
    border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
        
}
    .k-list-filter .k-searchbox.k-hover.k-input,.k-list-filter  .k-searchbox.k-state-hover.k-input,.k-list-filter  .k-searchbox.k-state-hovered.k-input,.k-list-filter  .k-searchbox:hover.k-input{
    border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
        
}
    .k-list-filter .k-searchbox.k-textbox.k-input-solid{
    color: $tb-iris-primary-grey;
            border-bottom-color: $tb-iris-neutral-grey-70;
            border-left-color: $tb-iris-neutral-grey-70;
            border-right-color: $tb-iris-neutral-grey-70;
            border-top-color: $tb-iris-neutral-grey-70;
        
}
    .k-list.k-list-md .k-list-content .k-list-ul .k-list-item{
    color: $tb-iris-primary-grey;
        
}
    .k-list-filter .k-searchbox.k-disabled.k-input-solid,.k-list-filter  .k-searchbox.k-state-disabled.k-input-solid,.k-list-filter  .k-searchbox:disabled.k-input-solid{
    color: $tb-iris-neutral-grey-70;
            background-color: $tb-iris-neutral-grey-5;
        
}
    .k-list-filter .k-searchbox.k-disabled,.k-list-filter .k-searchbox.k-state-disabled,.k-list-filter .k-searchbox:disabled{
    background-image: none;
        
}
    .k-list-filter .k-searchbox.k-disabled.k-input,.k-list-filter  .k-searchbox.k-state-disabled.k-input,.k-list-filter  .k-searchbox:disabled.k-input{
    border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
        
}
    .k-menu-popup .k-group.k-menu-group .k-item.k-menu-item.k-focus .k-link.k-menu-link,.k-menu-popup .k-group.k-menu-group  .k-item.k-menu-item.k-state-focus .k-link.k-menu-link,.k-menu-popup .k-group.k-menu-group  .k-item.k-menu-item.k-state-focused .k-link.k-menu-link,.k-menu-popup .k-group.k-menu-group  .k-item.k-menu-item:focus .k-link.k-menu-link{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-menu-popup .k-group.k-menu-group .k-item.k-menu-item.k-disabled,.k-menu-popup .k-group.k-menu-group .k-item.k-menu-item.k-state-disabled,.k-menu-popup .k-group.k-menu-group .k-item.k-menu-item:disabled{
    color: $tb-iris-neutral-grey-70;
            background-color: $tb-iris-neutral-grey-5;
            background-image: none;
        
}
    .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-selected{
    border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-bottom-style: solid;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-left-style: solid;
            border-left-width: $tb-iris-selected-border-width;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-right-style: solid;
            border-right-width: $tb-iris-selected-border-width;
            border-top-color: $tb-iris-primary-blue-dark-30;
            border-top-style: solid;
            border-top-width: $tb-iris-selected-border-width;
            background-color: initial;
            background-image: none;
        
}
    .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input{
    height: $tb-iris-form-input-height;
            color: $tb-iris-primary-grey;
            border-bottom-color: $tb-iris-neutral-grey-50;
            border-left-color: $tb-iris-neutral-grey-50;
            border-right-color: $tb-iris-neutral-grey-50;
            border-top-color: $tb-iris-neutral-grey-50;
            border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-input-md.k-rounded-md.k-input-outline.k-textbox.k-input{
    height: 32px;
        
}
    .k-input-md.k-rounded-md.k-input-flat.k-textbox.k-input{
    height: 32px;
        
}
    .k-datepicker.k-input{
    height: $tb-iris-form-input-height;
            border-bottom-style: solid;
            border-left-style: solid;
            border-right-style: solid;
            border-top-style: solid;
            border-right-width: $tb-iris-border-width;
            border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
            justify-content: center;
            align-items: center;
            align-self: center;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-datepicker.k-input.k-input-solid{
    border-bottom-color: $tb-iris-neutral-grey-50;
            border-left-color: $tb-iris-neutral-grey-50;
            border-right-color: $tb-iris-neutral-grey-50;
            border-top-color: $tb-iris-neutral-grey-50;
        
}
    .k-maskedtextbox{
    height: 32px;
        
}
    .k-maskedtextbox.k-input-solid{
    border-bottom-color: $tb-iris-neutral-grey-50;
            border-left-color: $tb-iris-neutral-grey-50;
            border-right-color: $tb-iris-neutral-grey-50;
            border-top-color: $tb-iris-neutral-grey-50;
        
}
    .k-numerictextbox{
    height: $tb-iris-form-input-height;
        
}
    .k-numerictextbox.k-input-solid{
    border-bottom-color: $tb-iris-neutral-grey-50;
            border-left-color: $tb-iris-neutral-grey-50;
            border-right-color: $tb-iris-neutral-grey-50;
            border-top-color: $tb-iris-neutral-grey-50;
        
}
    .k-numerictextbox.k-input{
    border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
            border-bottom-style: solid;
            border-left-style: solid;
            border-right-style: solid;
            border-top-style: solid;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-maskedtextbox .k-input-inner{
    height: $tb-iris-form-input-height;
        
}
    .k-input{
    height: $tb-iris-form-input-height;
        
}
    .k-input.k-input-solid{
    border-bottom-color: $tb-iris-neutral-grey-50;
            border-left-color: $tb-iris-neutral-grey-50;
            border-right-color: $tb-iris-neutral-grey-50;
            border-top-color: $tb-iris-neutral-grey-50;
        
}
    .k-textarea{
    min-height: $tb-iris-form-input-height;
            height: inherit;
        
}
    .k-daterangepicker .k-floating-label-container .k-dateinput .k-input-inner{
    height: $tb-iris-form-input-height;
        
}
    .k-dropdownlist{
    height: $tb-iris-form-input-height;
            border-bottom-style: solid;
            border-left-style: solid;
            border-right-style: solid;
            border-top-style: solid;
            color: $tb-kendo-component-text;
            border-bottom-color: $tb-iris-standard-border;
            border-left-color: $tb-iris-standard-border;
            border-right-color: $tb-iris-standard-border;
            border-top-color: $tb-iris-standard-border;
            justify-content: center;
            align-items: center;
            align-self: center;
            background-color: $tb-kendo-button-bg;
            background-image: none;
            display: inline-flex;
            padding-bottom: 0px;
            padding-left: 0px;
            padding-right: 0px;
            padding-top: 0px;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-dropdowntree{
    height: $tb-iris-form-input-height;
            border-bottom-color: $tb-iris-primary-blue;
            border-left-color: $tb-iris-primary-blue;
            border-right-color: $tb-iris-primary-blue;
            border-top-color: $tb-iris-primary-blue;
            background-color: $tb-kendo-button-bg;
            background-image: none;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item{
    border-bottom-color: $tb-iris-primary-blue;
            border-bottom-style: solid;
            border-bottom-width: $tb-iris-standard-border-width;
            border-left-color: $tb-iris-primary-blue;
            border-left-style: solid;
            border-left-width: $tb-iris-standard-border-width;
            border-right-color: $tb-iris-primary-blue;
            border-right-style: solid;
            border-right-width: $tb-iris-standard-border-width;
            border-top-color: $tb-iris-primary-blue;
            border-top-style: solid;
            border-top-width: $tb-iris-standard-border-width;
            border-bottom-left-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-top-left-radius: $tb-iris-border-radius;
            border-top-right-radius: $tb-iris-border-radius;
            position: relative;
            background-color: initial;
            background-image: none;
            height: 32px;
            flex-direction: row;
        
}
    .k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item .k-link.k-menu-link .k-icon.k-font-icon.k-i-caret-alt-down::before{
    content: "\e00d";
        
}
    .k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item .k-link.k-menu-link .k-icon.k-font-icon.k-i-caret-alt-down{
                    @extend %tb-typography-Iris-IFM-font-icons;
      
}
    .k-reset.k-header.k-menu.k-menu-vertical .k-item.k-menu-item .k-link.k-menu-link .k-icon.k-font-icon.k-i-caret-alt-right::before{
    content: "\e014";
        
}
    .k-menu-popup.k-popup .k-menu-group .k-item.k-menu-item .k-link.k-menu-link .k-icon.k-font-icon.k-i-caret-alt-right::before{
    content: "\e014";
        
}
    .k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item.k-disabled,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item.k-state-disabled,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item:disabled{
    color: $tb-iris-neutral-grey-70;
            background-color: $tb-iris-neutral-grey-5;
            background-image: none;
            border-bottom-color: $tb-iris-neutral-grey-70;
            border-left-color: $tb-iris-neutral-grey-70;
            border-right-color: $tb-iris-neutral-grey-70;
            border-top-color: $tb-iris-neutral-grey-70;
            border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
        
}
    .k-reset.k-header.k-menu.k-menu-vertical .k-item.k-menu-item.k-focus .k-link.k-menu-link,.k-reset.k-header.k-menu.k-menu-vertical  .k-item.k-menu-item.k-state-focus .k-link.k-menu-link,.k-reset.k-header.k-menu.k-menu-vertical  .k-item.k-menu-item.k-state-focused .k-link.k-menu-link,.k-reset.k-header.k-menu.k-menu-vertical  .k-item.k-menu-item:focus .k-link.k-menu-link{
                    @extend %tb-effects-iris-standard-inner-shadow;
      
}
    .k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item.k-focus,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item.k-state-focus,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item.k-state-focused,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item:focus{
                    @extend %tb-effects-iris-standard-inner-shadow;
          color: $tb-iris-primary-blue-dark-30;
            border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
        
}
    .k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item.k-hover,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item.k-state-hover,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item.k-state-hovered,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item:hover{
    border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
            border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
            color: $tb-iris-primary-blue-dark-30;
        
}
    .k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item .k-link.k-menu-link.k-active,.k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item  .k-link.k-menu-link.k-state-active,.k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item  .k-link.k-menu-link:active{
    color: $tb-iris-primary-blue-dark-30;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
            border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-bottom-style: none;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-left-style: none;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-right-style: none;
            border-top-color: $tb-iris-primary-blue-dark-30;
            border-top-style: none;
            border-bottom-left-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-top-left-radius: $tb-iris-border-radius;
            border-top-right-radius: $tb-iris-border-radius;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item.k-focus .k-link.k-menu-link,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item.k-state-focus .k-link.k-menu-link,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item.k-state-focused .k-link.k-menu-link,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item:focus .k-link.k-menu-link{
    border-bottom-left-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-top-left-radius: $tb-iris-border-radius;
            border-top-right-radius: $tb-iris-border-radius;
            row-gap: $tb-iris-button-gap;
            column-gap: $tb-iris-button-gap;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-toolbar.k-toolbar-md{
    column-gap: $tb-iris-button-container-gap;
            row-gap: $tb-iris-button-gap;
        
}
    .k-toolbar{
    background-color: $tb-iris-neutral-light-bg;
            background-image: none;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item .k-link.k-menu-link{
    row-gap: $tb-iris-button-gap;
            column-gap: $tb-iris-button-gap;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-dropdownlist .k-input-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down::before{
    content: "\e015";
        
}
    .k-dropdownlist .k-input-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down{
    display: flex;
        
}
    .k-dropdownlist .k-input-inner{
    display: inline-block;
            align-self: center;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-dropdownlist.k-hover,.k-dropdownlist.k-state-hover,.k-dropdownlist.k-state-hovered,.k-dropdownlist:hover{
    background-color: $tb-kendo-component-bg;
            background-image: none;
            justify-content: center;
            align-items: center;
            align-self: center;
            outline-style: none;
            padding-bottom: 0px;
            padding-left: 0px;
            padding-right: 0px;
            padding-top: 0px;
        
}
    .k-dropdownlist.k-hover.k-picker, .k-dropdownlist.k-state-hover.k-picker, .k-dropdownlist.k-state-hovered.k-picker, .k-dropdownlist:hover.k-picker{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
      
}
    .k-dropdownlist.k-picker.k-picker-md.k-rounded-md.k-picker-solid .k-input-button{
    border-top-right-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-bottom-style: hidden;
            border-left-style: hidden;
            border-right-style: hidden;
            border-top-style: hidden;
            width: $tb-iris-form-input-height;
            height: $tb-iris-form-input-height;
            background-color: transparent;
            background-image: none;
        
}
    .k-popup.k-list-container.k-dropdownlist-popup .k-list.k-list-md{
    border-top-color: $tb-iris-neutral-grey-50;
            border-top-style: solid;
            border-top-width: $tb-iris-standard-border-width;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-bottom-left-radius: $tb-iris-border-radius;
        
}
    .k-dropdownlist.k-focus,.k-dropdownlist.k-state-focus,.k-dropdownlist.k-state-focused,.k-dropdownlist:focus{
    padding-bottom: 0px;
            padding-left: 0px;
            padding-right: 0px;
            padding-top: 0px;
        
}
    .k-dropdownlist.k-focus.k-picker-solid, .k-dropdownlist.k-state-focus.k-picker-solid, .k-dropdownlist.k-state-focused.k-picker-solid, .k-dropdownlist:focus.k-picker-solid{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
      
}
    .k-dropdownlist.k-invalid,.k-dropdownlist.ng-invalid.ng-touched,.k-dropdownlist.ng-invalid.ng-dirty{
    border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            border-bottom-color: $tb-invalid;
            border-left-color: $tb-invalid;
            border-right-color: $tb-invalid;
            border-top-color: $tb-invalid;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
            color: $tb-iris-primary-red;
            padding-bottom: 0px;
            padding-left: 0px;
            padding-right: 0px;
            padding-top: 0px;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-dropdownlist.k-invalid.k-picker, .k-dropdownlist.ng-invalid.ng-touched.k-picker, .k-dropdownlist.ng-invalid.ng-dirty.k-picker{
                    @extend %tb-effects-iris-invalid-select-inner-shadow;
      
}
    .k-dropdownlist.k-invalid .k-input-inner, .k-dropdownlist.ng-invalid.ng-touched .k-input-inner, .k-dropdownlist.ng-invalid.ng-dirty .k-input-inner{
    border-bottom-color: $tb-iris-primary-red;
            border-left-color: $tb-iris-primary-red;
            border-right-color: $tb-iris-primary-red;
            border-top-color: $tb-iris-primary-red;
        
}
    .k-dropdownlist.k-invalid .k-input-button, .k-dropdownlist.ng-invalid.ng-touched .k-input-button, .k-dropdownlist.ng-invalid.ng-dirty .k-input-button{
    border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
        
}
    .k-dropdownlist.k-focus .k-input-inner, .k-dropdownlist.k-state-focus .k-input-inner, .k-dropdownlist.k-state-focused .k-input-inner, .k-dropdownlist:focus .k-input-inner{
    border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
            border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
        
}
    .k-dropdownlist.k-focus .k-input-button, .k-dropdownlist.k-state-focus .k-input-button, .k-dropdownlist.k-state-focused .k-input-button, .k-dropdownlist:focus .k-input-button{
    border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
        
}
    .k-scheduler .k-toolbar.k-scheduler-toolbar .k-toolbar-item .k-button-group.k-scheduler-navigation{
    height: $tb-iris-form-input-height;
        
}
    .k-scheduler .k-toolbar.k-scheduler-toolbar .k-button-group.k-scheduler-views{
    height: $tb-iris-form-input-height;
        
}
    .k-scheduler .k-event{
    color: $tb-iris-primary-grey;
        
}
    .k-chip{
    font-size: 12px;
            border-bottom-color: $tb-iris-secondary-blue;
            border-left-color: $tb-iris-secondary-blue;
            border-right-color: $tb-iris-secondary-blue;
            border-top-color: $tb-iris-secondary-blue;
            color: $tb-iris-neutral-light-bg;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            background-color: k-color( primary );
            background-image: none;
        
}
    .k-chip.k-chip-outline.k-chip-outline-base{
    color: $tb-iris-secondary-blue;
        
}
    .k-chip.k-disabled,.k-chip.k-state-disabled,.k-chip:disabled{
    background-color: $tb-iris-neutral-grey-5;
            background-image: none;
            color: $tb-iris-neutral-grey-70;
            border-bottom-color: $tb-iris-neutral-grey-70;
            border-left-color: $tb-iris-neutral-grey-70;
            border-right-color: $tb-iris-neutral-grey-70;
            border-top-color: $tb-iris-neutral-grey-70;
            border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
        
}
    .k-chip.k-chip-outline.k-disabled, .k-chip.k-chip-outline.k-state-disabled, .k-chip.k-chip-outline:disabled{
    color: $tb-iris-neutral-grey-70;
        
}
    .k-chip.k-chip-outline.k-hover.k-chip-outline-base, .k-chip.k-chip-outline.k-state-hover.k-chip-outline-base, .k-chip.k-chip-outline.k-state-hovered.k-chip-outline-base, .k-chip.k-chip-outline:hover.k-chip-outline-base{
    background-color: $tb-kendo-base-bg;
        
}
    .k-chip.k-chip-outline.k-hover, .k-chip.k-chip-outline.k-state-hover, .k-chip.k-chip-outline.k-state-hovered, .k-chip.k-chip-outline:hover{
    background-image: none;
        
}
    .k-chip.k-chip-outline.k-selected.k-chip-outline-base{
    background-color: $tb-kendo-base-bg;
        
}
    .k-chip.k-chip-outline.k-selected{
    background-image: none;
        
}
    .k-reset.k-header.k-menu.k-menu-horizontal{
    column-gap: $tb-iris-button-container-gap;
        
}
    .k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item.k-hover .k-link.k-menu-link,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item.k-state-hover .k-link.k-menu-link,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item.k-state-hovered .k-link.k-menu-link,.k-reset.k-header.k-menu.k-menu-horizontal  .k-item.k-menu-item:hover .k-link.k-menu-link{
    margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-reset.k-header.k-menu.k-menu-vertical .k-item.k-menu-item.k-disabled,.k-reset.k-header.k-menu.k-menu-vertical  .k-item.k-menu-item.k-state-disabled,.k-reset.k-header.k-menu.k-menu-vertical  .k-item.k-menu-item:disabled{
    color: $tb-iris-neutral-grey-70;
            background-color: $tb-iris-neutral-grey-5;
            background-image: none;
        
}
    .k-button-group{
    height: 32px;
        
}
    .k-dropdownlist .k-input-button{
    color: $tb-kendo-button-text;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
            padding-bottom: 0px;
            padding-left: 0px;
            padding-right: 0px;
            padding-top: 0px;
            border-bottom-left-radius: $tb-iris-border-radius;
            border-top-left-radius: $tb-iris-border-radius;
        
}
    .k-dropdownlist .k-input-button.k-button{
    border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-dropdownlist.k-picker.k-picker-md.k-rounded-md.k-picker-solid .k-input-button{
    height: 31px;
            width: 31px;
        
}
    .k-datepicker.k-input .k-input-inner{
    border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            display: inline-block;
            align-self: center;
            position: relative;
        
}
    .k-datepicker.k-input.k-disabled .k-input-inner, .k-datepicker.k-input.k-state-disabled .k-input-inner, .k-datepicker.k-input:disabled .k-input-inner{
    border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
            background-color: $tb-iris-neutral-grey-5;
            background-image: none;
        
}
    .k-datepicker.k-input.k-disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button, .k-datepicker.k-input.k-state-disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button, .k-datepicker.k-input:disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button{
    color: $tb-iris-neutral-grey-70;
            background-color: $tb-iris-neutral-grey-5;
            background-image: none;
            border-bottom-color: $tb-iris-neutral-grey-70;
            border-left-color: $tb-iris-neutral-grey-70;
            border-right-color: $tb-iris-neutral-grey-70;
            border-top-color: $tb-iris-neutral-grey-70;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-numerictextbox .k-input-inner{
    border-bottom-color: $tb-iris-neutral-grey-50;
            border-bottom-style: none;
            border-bottom-width: $tb-iris-standard-border-width;
            border-left-color: $tb-iris-neutral-grey-50;
            border-left-style: none;
            border-left-width: $tb-iris-standard-border-width;
            border-right-color: $tb-iris-neutral-grey-50;
            border-right-style: none;
            border-right-width: $tb-iris-standard-border-width;
            border-top-color: $tb-iris-neutral-grey-50;
            border-top-style: none;
            border-top-width: $tb-iris-standard-border-width;
            border-top-left-radius: $tb-iris-border-radius;
            border-bottom-left-radius: $tb-iris-border-radius;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-top-right-radius: $tb-iris-border-radius;
        
}
    .k-numerictextbox .k-input-spinner.k-spin-button{
    border-top-right-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
            border-bottom-color: $tb-iris-primary-blue;
            border-bottom-style: none;
            border-left-color: $tb-iris-primary-blue;
            border-left-style: none;
            border-right-color: $tb-iris-primary-blue;
            border-right-style: none;
            border-top-color: $tb-iris-primary-blue;
            border-top-style: none;
            height: $tb-iris-form-input-height;
        
}
    .k-numerictextbox .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase{
    margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
            padding-top: 6px;
            padding-bottom: 6px;
            border-top-right-radius: $tb-iris-border-radius;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            border-bottom-width: 0px;
            border-left-width: 0px;
            border-right-width: 0px;
            border-top-width: 0px;
        
}
    .k-numerictextbox .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease{
    padding-top: 6px;
            padding-bottom: 6px;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-bottom-width: 0px;
            border-left-width: 0px;
            border-right-width: 0px;
            border-top-width: 0px;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-numerictextbox .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-up::before{
    content: "\e013";
        
}
    .k-numerictextbox .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-up{
    bottom: -1px;
            display: flex;
        
}
    .k-numerictextbox .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down::before{
    content: "\e015";
        
}
    .k-numerictextbox .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down{
    top: -1px;
            display: flex;
        
}
    .k-numerictextbox .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease.k-hover,.k-numerictextbox .k-input-spinner.k-spin-button  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease.k-state-hover,.k-numerictextbox .k-input-spinner.k-spin-button  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease.k-state-hovered,.k-numerictextbox .k-input-spinner.k-spin-button  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease:hover{
    margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
            border-bottom-width: 0px;
            border-left-width: 0px;
            border-right-width: 0px;
            border-top-width: 0px;
            color: $tb-kendo-button-text;
        
}
    .k-numerictextbox .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase.k-hover,.k-numerictextbox .k-input-spinner.k-spin-button  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase.k-state-hover,.k-numerictextbox .k-input-spinner.k-spin-button  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase.k-state-hovered,.k-numerictextbox .k-input-spinner.k-spin-button  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase:hover{
    border-bottom-width: 0px;
            border-left-width: 0px;
            border-right-width: 0px;
            border-top-width: 0px;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
            color: $tb-kendo-button-text;
        
}
    .k-combobox.k-input{
    border-bottom-style: solid;
            border-left-style: solid;
            border-right-style: solid;
            border-top-style: solid;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-combobox.k-input .k-input-inner{
    border-top-left-radius: $tb-iris-border-radius;
            border-bottom-left-radius: $tb-iris-border-radius;
            border-bottom-color: $tb-iris-neutral-grey-50;
            border-bottom-style: none;
            border-bottom-width: $tb-iris-standard-border-width;
            border-left-color: $tb-iris-neutral-grey-50;
            border-left-style: none;
            border-left-width: $tb-iris-standard-border-width;
            border-right-color: $tb-iris-neutral-grey-50;
            border-right-style: none;
            border-right-width: $tb-iris-standard-border-width;
            border-top-color: $tb-iris-neutral-grey-50;
            border-top-style: none;
            border-top-width: $tb-iris-standard-border-width;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-combobox.k-input .k-input-button{
    border-top-right-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            align-self: center;
            background-color: transparent;
            width: $tb-iris-form-input-height;
            height: $tb-iris-form-input-height;
        
}
    .k-combobox.k-input .k-input-button.k-button{
    background-image: none;
        
}
    .k-combobox.k-input .k-input-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down::before{
    content: "\e015";
        
}
    .k-scheduler .k-toolbar.k-scheduler-toolbar .k-toolbar-item .k-button-group.k-scheduler-navigation .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-group-start,.k-scheduler .k-toolbar.k-scheduler-toolbar .k-toolbar-item .k-button-group.k-scheduler-navigation .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md:first-child{
    background-color: $tb-kendo-button-bg;
            background-image: none;
        
}
    .k-card .k-actions.k-card-actions{
    border-bottom-right-radius: $tb-iris-border-radius;
            border-bottom-left-radius: $tb-iris-border-radius;
            border-bottom-color: $tb-iris-neutral-grey-20;
            border-left-color: $tb-iris-neutral-grey-20;
            border-right-color: $tb-iris-neutral-grey-20;
            border-top-color: $tb-iris-neutral-grey-20;
            border-top-style: solid;
            border-top-width: $tb-iris-standard-border-width;
            background-color: initial;
            background-image: none;
            justify-content: end;
        
}
    .k-card .k-card-header{
    background-color: $tb-iris-neutral-panel-heading;
            background-image: none;
            border-top-left-radius: $tb-iris-border-radius;
            border-top-right-radius: $tb-iris-border-radius;
            border-bottom-color: $tb-iris-neutral-grey-20;
            border-left-color: $tb-iris-neutral-grey-20;
            border-right-color: $tb-iris-neutral-grey-20;
            border-top-color: $tb-iris-neutral-grey-20;
            padding-top: 8px;
            padding-bottom: 8px;
        
}
    .k-card{
    border-bottom-color: $tb-iris-neutral-grey-20;
            border-left-color: $tb-iris-neutral-grey-20;
            border-right-color: $tb-iris-neutral-grey-20;
            border-top-color: $tb-iris-neutral-grey-20;
            border-bottom-left-radius: 0px;
            border-bottom-right-radius: 0px;
            border-top-left-radius: 0px;
            border-top-right-radius: 0px;
        
}
    .k-splitter.k-splitter-flex.k-splitter-horizontal .k-splitbar.k-splitbar-horizontal{
    width: 8px;
            color: $tb-iris-neutral-grey-70;
            background-color: initial;
            background-image: none;
            margin-bottom: 0px;
            margin-left: 8px;
            margin-right: 8px;
            margin-top: 0px;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
            border-bottom-color: $tb-iris-neutral-grey-20;
            border-left-color: $tb-iris-neutral-grey-20;
            border-right-color: $tb-iris-neutral-grey-20;
            border-top-color: $tb-iris-neutral-grey-20;
            outline-color: $tb-iris-neutral-grey-20;
            outline-style: solid;
            outline-width: $tb-iris-border-width;
            flex-direction: column;
        
}
    .k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical{
    height: 8px;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            background-color: initial;
            background-image: none;
            color: $tb-iris-neutral-grey-70;
            border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
            border-bottom-color: $tb-iris-neutral-grey-20;
            border-left-color: $tb-iris-neutral-grey-20;
            border-right-color: $tb-iris-neutral-grey-20;
            border-top-color: $tb-iris-neutral-grey-20;
            outline-color: $tb-iris-neutral-grey-20;
            outline-style: solid;
            outline-width: $tb-iris-border-width;
            margin-top: 8px;
            margin-bottom: 8px;
        
}
    .k-splitter.k-splitter-flex.k-splitter-horizontal .k-splitbar.k-splitbar-horizontal.k-hover,.k-splitter.k-splitter-flex.k-splitter-horizontal  .k-splitbar.k-splitbar-horizontal.k-state-hover,.k-splitter.k-splitter-flex.k-splitter-horizontal  .k-splitbar.k-splitbar-horizontal.k-state-hovered,.k-splitter.k-splitter-flex.k-splitter-horizontal  .k-splitbar.k-splitbar-horizontal:hover{
    background-color: $tb-iris-neutral-grey-40;
            background-image: none;
            color: $tb-iris-neutral-light-bg;
        
}
    .k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical.k-focus .k-collapse-prev,.k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical.k-state-focus .k-collapse-prev,.k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical.k-state-focused .k-collapse-prev,.k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical:focus .k-collapse-prev{
    height: 8px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        
}
    .k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical .k-collapse-prev .k-icon.k-font-icon{
    justify-content: center;
            display: inline-flex;
        
}
    .k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical.k-focus,.k-splitter.k-splitter-flex.k-splitter-vertical  .k-splitbar.k-splitbar-vertical.k-state-focus,.k-splitter.k-splitter-flex.k-splitter-vertical  .k-splitbar.k-splitbar-vertical.k-state-focused,.k-splitter.k-splitter-flex.k-splitter-vertical  .k-splitbar.k-splitbar-vertical:focus{
    display: inline-flex;
            flex-direction: row;
            border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
            border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
            color: $tb-iris-neutral-light-bg;
            background-color: $tb-iris-neutral-grey-50;
            background-image: none;
        
}
    .k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical.k-focus .k-collapse-next,.k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical.k-state-focus .k-collapse-next,.k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical.k-state-focused .k-collapse-next,.k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical:focus .k-collapse-next{
    align-self: center;
            display: inline-flex;
            align-items: center;
        
}
    .k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical .k-collapse-prev{
    display: inline-flex;
            align-items: center;
            align-self: center;
            margin-right: 0.25rem;
        
}
    .k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical .k-collapse-next{
    display: inline-flex;
            align-items: center;
            align-self: center;
        
}
    .k-splitter.k-splitter-flex.k-splitter-horizontal .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal .k-resize-handle{
    width: 3px;
            height: 21px;
        
}
    .k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical.k-splitbar-draggable-vertical .k-resize-handle{
    width: 21px;
            height: 3px;
        
}
    .k-splitter.k-splitter-flex.k-splitter-horizontal .k-splitbar.k-splitbar-horizontal.k-focus,.k-splitter.k-splitter-flex.k-splitter-horizontal  .k-splitbar.k-splitbar-horizontal.k-state-focus,.k-splitter.k-splitter-flex.k-splitter-horizontal  .k-splitbar.k-splitbar-horizontal.k-state-focused,.k-splitter.k-splitter-flex.k-splitter-horizontal  .k-splitbar.k-splitbar-horizontal:focus{
    color: $tb-iris-neutral-light-bg;
            border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
            border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
            background-color: $tb-iris-neutral-grey-50;
            background-image: none;
        
}
    .k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical.k-hover,.k-splitter.k-splitter-flex.k-splitter-vertical  .k-splitbar.k-splitbar-vertical.k-state-hover,.k-splitter.k-splitter-flex.k-splitter-vertical  .k-splitbar.k-splitbar-vertical.k-state-hovered,.k-splitter.k-splitter-flex.k-splitter-vertical  .k-splitbar.k-splitbar-vertical:hover{
    color: $tb-iris-neutral-light-bg;
            background-color: $tb-iris-neutral-grey-40;
            background-image: none;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item{
    border-bottom-color: $tb-iris-neutral-grey-70;
            border-left-color: $tb-iris-neutral-grey-70;
            border-right-color: $tb-iris-neutral-grey-70;
            border-top-color: $tb-iris-neutral-grey-70;
            border-top-left-radius: $tb-iris-border-radius;
            border-top-right-radius: $tb-iris-border-radius;
            border-top-style: solid;
            border-top-width: 1px;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
            height: 36px;
            color: $tb-iris-neutral-grey-70;
            line-height: 1.4;
            background-color: $tb-iris-neutral-light-bg;
            background-image: none;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active .k-link,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-state-active .k-link,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item:active .k-link{
    color: $tb-iris-neutral-light-bg;
            background-color: initial;
            background-image: none;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-active,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item:active{
    background-color: $tb-iris-primary-grey;
            border-top-color: $tb-iris-primary-grey;
            border-top-style: none;
            border-top-width: 1px;
            border-left-color: $tb-iris-primary-grey;
            border-left-style: solid;
            border-left-width: 1px;
            border-right-color: $tb-iris-primary-grey;
            border-right-style: solid;
            border-right-width: 1px;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-state-active,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item:active{
    background-image: none;
            border-bottom-color: $tb-iris-secondary-blue;
            border-bottom-style: solid;
            border-bottom-width: 4px;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item .k-link{
    padding-right: 16px;
            padding-left: 16px;
            padding-top: 8px;
            padding-bottom: 8px;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-hovered,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item:hover{
    border-bottom-color: $tb-iris-primary-blue;
            border-left-color: $tb-iris-primary-blue;
            border-right-color: $tb-iris-primary-blue;
            border-top-color: $tb-iris-primary-blue;
            color: $tb-iris-primary-blue;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-focused,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item:focus{
                    @extend %tb-effects-tb-internal-none-effects;
          color: $tb-iris-primary-blue;
            border-bottom-color: $tb-iris-primary-blue;
            border-left-color: $tb-iris-primary-blue;
            border-right-color: $tb-iris-primary-blue;
            border-top-color: $tb-iris-primary-blue;
            outline-style: none;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-disabled,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-disabled,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item:disabled{
    border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-disabled,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-state-disabled,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item:disabled{
    background-color: $tb-iris-neutral-grey-5;
            background-image: none;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active.k-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-state-active.k-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item:active.k-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active.k-state-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active.k-state-hovered,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active:hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-state-active.k-state-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-state-active.k-state-hovered,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-state-active:hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item:active.k-state-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item:active.k-state-hovered,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item:active:hover{
    border-bottom-color: $tb-iris-secondary-blue;
            border-bottom-style: solid;
            border-bottom-width: 4px;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active.k-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-active.k-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item:active.k-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-active.k-state-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-active.k-state-hovered,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-active:hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-active.k-state-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-active.k-state-hovered,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-active:hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item:active.k-state-hover,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item:active.k-state-hovered,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item:active:hover{
    border-left-color: $tb-iris-primary-grey;
            border-right-color: $tb-iris-primary-grey;
            border-top-color: $tb-iris-primary-grey;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active.k-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-state-active.k-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item:active.k-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active.k-state-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active.k-state-focused,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active:focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-state-active.k-state-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-state-active.k-state-focused,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-state-active:focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item:active.k-state-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item:active.k-state-focused,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item:active:focus{
    border-bottom-color: $tb-iris-secondary-blue;
            border-bottom-style: solid;
            border-bottom-width: 4px;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-active.k-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-active.k-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item:active.k-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-active.k-state-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-active.k-state-focused,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-active:focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-active.k-state-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-active.k-state-focused,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item.k-state-active:focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item:active.k-state-focus,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item:active.k-state-focused,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset  .k-item.k-tabstrip-item:active:focus{
                    @extend %tb-effects-tb-internal-none-effects;
          border-left-color: $tb-iris-primary-grey;
            border-right-color: $tb-iris-primary-grey;
            border-top-color: $tb-iris-primary-grey;
        
}
    .k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table.k-table-md .k-table-thead .k-table-row .k-header.k-table-th{
    font-weight: bold;
            border-bottom-width: 0px;
            border-left-width: 0px;
            border-right-width: 0px;
            border-top-width: 0px;
            border-bottom-color: $tb-iris-neutral-light-bg;
            border-left-color: $tb-iris-neutral-light-bg;
            border-right-color: $tb-iris-neutral-light-bg;
            border-top-color: $tb-iris-neutral-light-bg;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-grid.k-grid-md .k-grouping-header .k-chip-list.k-chip-list-md .k-chip.k-chip-md.k-chip-solid.k-chip-solid-base.k-rounded-md .k-icon.k-font-icon.k-i-sort-desc-small{
    color: $tb-iris-neutral-light-bg;
        
}
    .k-grid.k-grid-md .k-grouping-header .k-chip-list.k-chip-list-md .k-chip.k-chip-md.k-chip-solid.k-chip-solid-base.k-rounded-md .k-chip-actions .k-chip-action.k-chip-remove-action{
    color: $tb-iris-neutral-light-bg;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-table-tbody .k-table-row.k-master-row{
    background-color: $tb-iris-neutral-grey-5;
            background-image: none;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-table-tbody .k-master-row.k-table-row.k-alt{
    background-color: $tb-iris-neutral-light-bg;
            background-image: none;
        
}
    .k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table.k-table-md .k-table-thead .k-table-row{
    background-color: $tb-iris-neutral-light-bg;
            background-image: none;
            border-bottom-color: unset;
            border-left-color: unset;
            border-right-color: unset;
            border-top-color: unset;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-sorted{
    background-color: initial;
            background-image: none;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-table-tbody .k-table-row.k-master-row.k-hover,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-table-tbody  .k-table-row.k-master-row.k-state-hover,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-table-tbody  .k-table-row.k-master-row.k-state-hovered,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-table-tbody  .k-table-row.k-master-row:hover{
    background-color: $tb-iris-primary-blue-dark-10;
            background-image: none;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-table.k-grid-table.k-table-md .k-table-tbody .k-master-row.k-table-row.k-selected >td{
    background-color: $tb-iris-secondary-blue-10;
            background-image: none;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-table-tbody .k-master-row.k-table-row.k-selected >td{
    background-color: initial;
            background-image: none;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-table-tbody .k-master-row.k-table-row.k-selected.k-hover,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-table-tbody  .k-master-row.k-table-row.k-selected.k-state-hover,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-table-tbody  .k-master-row.k-table-row.k-selected.k-state-hovered,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-table-tbody  .k-master-row.k-table-row.k-selected:hover{
    background-color: $tb-iris-secondary-blue-10;
            background-image: linear-gradient($tb-iris-primary-blue-dark-10, $tb-iris-primary-blue-dark-10);
        
}
    .k-grid.k-grid-md{
    border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            border-bottom-width: 0px;
            border-left-width: 0px;
            border-right-width: 0px;
            border-top-width: 0px;
            border-bottom-color: $tb-iris-neutral-light-bg;
            border-left-color: $tb-iris-neutral-light-bg;
            border-right-color: $tb-iris-neutral-light-bg;
            border-top-color: $tb-iris-neutral-light-bg;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-table.k-grid-table.k-table-md .k-table-tbody .k-master-row.k-table-row >td{
    border-left-color: $tb-iris-neutral-light-bg;
            border-left-style: none;
            border-left-width: 0px;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table .k-table-tbody .k-master-row.k-table-row.k-selected{
                    @extend %tb-effects-iris-standard-inner-shadow;
          background-color: $tb-iris-secondary-blue-10;
            background-image: none;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table .k-grouping-row .k-reset .k-icon.k-i-caret-alt-right::before{
    content: "\e014";
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table .k-grouping-row .k-reset .k-icon.k-i-caret-alt-right{
    color: $tb-iris-secondary-blue;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table .k-grouping-row .k-reset .k-icon.k-i-caret-alt-down::before{
    content: "\e015";
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table .k-grouping-row .k-reset .k-icon.k-i-caret-alt-down{
    color: $tb-iris-secondary-blue;
        
}
    .k-grid.k-grid-md .k-grouping-header{
    background-color: $tb-iris-neutral-light-bg;
            background-image: none;
            padding-bottom: 4px;
            padding-left: 4px;
            padding-right: 4px;
            padding-top: 4px;
        
}
    .k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table.k-table-md .k-table-thead .k-table-row .k-header.k-table-th.k-focus,.k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table.k-table-md .k-table-thead .k-table-row  .k-header.k-table-th.k-state-focus,.k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table.k-table-md .k-table-thead .k-table-row  .k-header.k-table-th.k-state-focused,.k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table.k-table-md .k-table-thead .k-table-row  .k-header.k-table-th:focus{
                    @extend %tb-effects-iris-standard-inner-shadow;
          border-right-color: $tb-iris-primary-blue-dark-30;
            border-right-style: solid;
            border-right-width: $tb-iris-selected-border-width;
        
}
    .k-pager{
    background-color: initial;
            background-image: none;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            color: $tb-iris-primary-grey;
            padding-top: 8px;
            padding-bottom: 4px;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-first .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-left::before{
    content: "\e00f";
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-first .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-left{
                    @extend %tb-typography-Iris-IFM-font-icons;
      
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-last .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-right::before{
    content: "\e010";
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-last .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-right{
                    @extend %tb-typography-Iris-IFM-font-icons;
      
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-first.k-hover.k-button-flat,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first.k-state-hover.k-button-flat,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first.k-state-hovered.k-button-flat,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first:hover.k-button-flat{
    background-color: initial !important;
            background-image: none !important;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-first.k-hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-left::before,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first.k-state-hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-left::before,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first.k-state-hovered .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-left::before,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first:hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-left::before{
    content: "\e00f";
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-first.k-hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-left,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first.k-state-hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-left,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first.k-state-hovered .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-left,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first:hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-left{
    font-weight: bold;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-last.k-hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-right,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-last.k-state-hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-right,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-last.k-state-hovered .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-right,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-last:hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-to-right{
    font-weight: bold;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-first.k-disabled,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first.k-state-disabled,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first:disabled{
    color: $tb-iris-neutral-grey-70;
        
}
    .k-pager .k-pager-numbers-wrap .k-pager-numbers .k-button.k-button-flat.k-button-flat-primary.k-selected{
    background-color: $tb-iris-primary-iris-blue-10 !important;
            background-image: none !important;
            color: $tb-iris-primary-blue;
            font-weight: 600;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-left::before{
    content: "\e016";
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-right::before{
    content: "\e014";
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-first{
    color: $tb-iris-primary-grey;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-hover,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-state-hover,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-state-hovered,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav:hover{
    color: $tb-iris-primary-blue-dark-30;
        
}
    .k-pager .k-pager-numbers-wrap .k-pager-numbers .k-button.k-button-flat.k-button-flat-primary{
    color: $tb-iris-primary-grey;
        
}
    .k-pager .k-pager-numbers-wrap .k-pager-numbers .k-button.k-button-flat.k-button-flat-primary.k-selected::before{
    background-color: initial;
            background-image: none;
        
}
    .k-pager .k-pager-numbers-wrap .k-pager-numbers .k-button.k-button-flat.k-button-flat-primary.k-hover::before,.k-pager .k-pager-numbers-wrap .k-pager-numbers  .k-button.k-button-flat.k-button-flat-primary.k-state-hover::before,.k-pager .k-pager-numbers-wrap .k-pager-numbers  .k-button.k-button-flat.k-button-flat-primary.k-state-hovered::before,.k-pager .k-pager-numbers-wrap .k-pager-numbers  .k-button.k-button-flat.k-button-flat-primary:hover::before{
    background-color: initial;
            background-image: none;
        
}
    .k-pager .k-pager-numbers-wrap .k-pager-numbers .k-button.k-button-flat.k-button-flat-primary.k-hover,.k-pager .k-pager-numbers-wrap .k-pager-numbers .k-button.k-button-flat.k-button-flat-primary.k-state-hover,.k-pager .k-pager-numbers-wrap .k-pager-numbers .k-button.k-button-flat.k-button-flat-primary.k-state-hovered,.k-pager .k-pager-numbers-wrap .k-pager-numbers .k-button.k-button-flat.k-button-flat-primary:hover{
    color: $tb-iris-primary-blue-dark-30;
            font-weight: 600;
        
}
    .k-pager .k-pager-numbers-wrap .k-pager-numbers .k-button.k-button-flat.k-button-flat-primary.k-focus::after,.k-pager .k-pager-numbers-wrap .k-pager-numbers  .k-button.k-button-flat.k-button-flat-primary.k-state-focus::after,.k-pager .k-pager-numbers-wrap .k-pager-numbers  .k-button.k-button-flat.k-button-flat-primary.k-state-focused::after,.k-pager .k-pager-numbers-wrap .k-pager-numbers  .k-button.k-button-flat.k-button-flat-primary:focus::after{
    box-shadow: none;
        
}
    .k-pager .k-pager-numbers-wrap .k-pager-numbers .k-button.k-button-flat.k-button-flat-primary::before{
    background-color: initial;
            background-image: none;
        
}
    .k-pager .k-pager-sizes .k-dropdownlist{
    background-color: initial;
            background-image: none;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-first.k-hover::before,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first.k-state-hover::before,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first.k-state-hovered::before,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-first:hover::before{
    background-color: initial;
            background-image: none;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-first::before{
                    @extend %tb-typography-Iris-IFM-font-icons;
          background-color: initial;
            background-image: none;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-last.k-hover::before,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-last.k-state-hover::before,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-last.k-state-hovered::before,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-pager-last:hover::before{
    background-color: initial;
            background-image: none;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-hover::before,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-state-hover::before,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-state-hovered::before,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav:hover::before{
    background-color: initial;
            background-image: none;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-left,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-state-hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-left,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-state-hovered .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-left,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav:hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-left{
    font-weight: bold;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-right,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-state-hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-right,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav.k-state-hovered .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-right,.k-pager .k-pager-numbers-wrap  .k-button.k-pager-nav:hover .k-icon.k-font-icon.k-button-icon.k-i-caret-alt-right{
    font-weight: bold;
        
}
    .k-pager .k-pager-numbers-wrap .k-button.k-pager-nav::before{
    background-color: initial;
            background-image: none;
        
}
    .k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table.k-table-md .k-table-thead .k-table-row .k-header.k-table-th.k-sorted .k-cell-inner .k-link .k-sort-icon .k-icon.k-i-sort-asc-small{
    font-size: 18px;
        
}
    .k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table.k-table-md .k-table-thead .k-table-row .k-header.k-table-th.k-sorted .k-cell-inner .k-link .k-sort-icon .k-icon.k-i-sort-desc-small{
    font-size: 18px;
        
}
    .k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table.k-table-md .k-table-thead .k-table-row .k-header.k-table-th.k-sorted{
    background-color: $tb-iris-secondary-blue-10;
            background-image: none;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-table.k-grid-table.k-table-md .k-table-tbody .k-master-row.k-table-row td.k-focus,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-table.k-grid-table.k-table-md .k-table-tbody .k-master-row.k-table-row  td.k-state-focus,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-table.k-grid-table.k-table-md .k-table-tbody .k-master-row.k-table-row  td.k-state-focused,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-table.k-grid-table.k-table-md .k-table-tbody .k-master-row.k-table-row  td:focus{
                    @extend %tb-effects-iris-standard-inner-shadow;
      
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table .k-master-row.k-table-row.k-grid-edit-row .k-grid-edit-cell .k-input{
    border-bottom-color: $tb-iris-neutral-grey-50;
            border-left-color: $tb-iris-neutral-grey-50;
            border-right-color: $tb-iris-neutral-grey-50;
            border-top-color: $tb-iris-neutral-grey-50;
            border-bottom-left-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-top-left-radius: $tb-iris-border-radius;
            border-top-right-radius: $tb-iris-border-radius;
            border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table .k-master-row.k-table-row.k-grid-edit-row .k-grid-edit-cell{
    background-color: $tb-iris-primary-blue-dark-10;
            background-image: none;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-table.k-grid-table.k-table-md .k-table-tbody .k-master-row.k-table-row.k-grid-edit-row .k-command-cell.k-focus,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-table.k-grid-table.k-table-md .k-table-tbody .k-master-row.k-table-row.k-grid-edit-row  .k-command-cell.k-state-focus,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-table.k-grid-table.k-table-md .k-table-tbody .k-master-row.k-table-row.k-grid-edit-row  .k-command-cell.k-state-focused,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-table.k-grid-table.k-table-md .k-table-tbody .k-master-row.k-table-row.k-grid-edit-row  .k-command-cell:focus{
                    @extend %tb-effects-tb-internal-none-effects;
          border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table .k-master-row.k-table-row.k-grid-edit-row .k-grid-edit-cell.k-focus,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table .k-master-row.k-table-row.k-grid-edit-row  .k-grid-edit-cell.k-state-focus,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table .k-master-row.k-table-row.k-grid-edit-row  .k-grid-edit-cell.k-state-focused,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table .k-master-row.k-table-row.k-grid-edit-row  .k-grid-edit-cell:focus{
    box-shadow: none;
        
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell .k-grid-edit-command.k-focus.k-button-solid-primary,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell  .k-grid-edit-command.k-state-focus.k-button-solid-primary,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell  .k-grid-edit-command.k-state-focused.k-button-solid-primary,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell  .k-grid-edit-command:focus.k-button-solid-primary{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell .k-grid-save-command.k-focus.k-button-solid-primary,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell  .k-grid-save-command.k-state-focus.k-button-solid-primary,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell  .k-grid-save-command.k-state-focused.k-button-solid-primary,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell  .k-grid-save-command:focus.k-button-solid-primary{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell .k-grid-remove-command.k-focus.k-button-solid-base,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell  .k-grid-remove-command.k-state-focus.k-button-solid-base,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell  .k-grid-remove-command.k-state-focused.k-button-solid-base,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell  .k-grid-remove-command:focus.k-button-solid-base{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell .k-grid-cancel-command.k-focus.k-button-solid-base,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell  .k-grid-cancel-command.k-state-focus.k-button-solid-base,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell  .k-grid-cancel-command.k-state-focused.k-button-solid-base,.k-grid.k-grid-md .k-grid-container .k-grid-content .k-grid-table.k-table.k-table-md .k-table-tbody .k-table-row.k-master-row.k-grid-edit-row .k-command-cell  .k-grid-cancel-command:focus.k-button-solid-base{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell{
    height: 32px;
        
}
    .k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button.k-disabled,.k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button.k-state-disabled,.k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button:disabled{
    height: 32px;
        
}
    .k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button.k-clear-button-visible{
    height: 32px;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table .k-table-thead .k-table-row.k-filter-row{
    background-color: $tb-iris-neutral-light-bg;
            background-image: none;
        
}
    .k-timepicker.k-input .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button{
    border-top-right-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-pager .k-pager-sizes .k-dropdownlist .k-input-inner{
    width: 60px;
        
}
    .k-window .k-window-titlebar{
    background-color: $tb-iris-primary-blue;
            background-image: none;
            color: $tb-iris-neutral-light-bg;
            backdrop-filter: none;
            filter: none;
        
}
    .k-window .k-window-content{
    border-right-color: $tb-iris-neutral-grey-20;
            border-right-style: solid;
            border-right-width: $tb-iris-border-width;
            border-bottom-color: $tb-iris-neutral-grey-20;
            border-bottom-style: solid;
            border-bottom-width: $tb-iris-border-width;
            border-left-color: $tb-iris-neutral-grey-20;
            border-left-style: solid;
            border-left-width: $tb-iris-border-width;
            background-color: $tb-iris-neutral-light-bg;
            background-image: none;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-content.k-dialog-content{
    border-bottom-right-radius: unset;
            border-bottom-left-radius: unset;
        
}
    .k-dialog-wrapper .k-window.k-dialog{
    border-bottom-left-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-top-left-radius: $tb-iris-border-radius;
            border-top-right-radius: $tb-iris-border-radius;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-actions.k-dialog-actions.k-actions.k-actions-stretched{
    border-bottom-right-radius: $tb-iris-border-radius;
            border-bottom-left-radius: $tb-iris-border-radius;
            border-bottom-color: $tb-iris-neutral-grey-20;
            border-bottom-width: $tb-iris-border-width;
            border-left-color: $tb-iris-neutral-grey-20;
            border-left-width: $tb-iris-border-width;
            border-right-color: $tb-iris-neutral-grey-20;
            border-right-width: $tb-iris-border-width;
            border-top-color: $tb-iris-neutral-grey-20;
            border-top-style: solid;
            border-top-width: 0px;
        
}
    .k-splitter.k-splitter-flex .k-icon.k-font-icon.k-i-caret-alt-left.k-icon-xs{
    font-size: 10px;
        
}
    .k-splitter.k-splitter-flex .k-icon.k-font-icon.k-i-caret-alt-right.k-icon-xs{
    font-size: 10px;
        
}
    .k-splitter.k-splitter-flex .k-icon.k-font-icon.k-i-caret-alt-down.k-icon-xs{
    font-size: 10px;
        
}
    .k-splitter.k-splitter-flex .k-icon.k-font-icon.k-i-caret-alt-up.k-icon-xs{
    font-size: 10px;
        
}
    .k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical.k-splitbar-draggable-vertical .k-collapse-prev{
    margin-right: 7px;
        
}
    .k-timepicker.k-input{
    border-bottom-style: solid;
            border-left-style: solid;
            border-right-style: solid;
            border-top-style: solid;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-timepicker.k-input .k-input-inner{
    border-bottom-color: $tb-iris-neutral-grey-50;
            border-bottom-style: none;
            border-bottom-width: $tb-iris-border-width;
            border-left-color: $tb-iris-neutral-grey-50;
            border-left-style: none;
            border-left-width: $tb-iris-border-width;
            border-right-color: $tb-iris-neutral-grey-50;
            border-right-style: none;
            border-right-width: $tb-iris-border-width;
            border-top-color: $tb-iris-neutral-grey-50;
            border-top-style: none;
            border-top-width: $tb-iris-border-width;
            border-top-left-radius: $tb-iris-border-radius;
            border-bottom-left-radius: $tb-iris-border-radius;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-datetimepicker.k-input .k-input-inner{
    margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
            border-bottom-color: $tb-iris-neutral-grey-50;
            border-bottom-style: none;
            border-bottom-width: $tb-iris-border-width;
            border-left-color: $tb-iris-neutral-grey-50;
            border-left-style: none;
            border-left-width: $tb-iris-border-width;
            border-right-color: $tb-iris-neutral-grey-50;
            border-right-style: none;
            border-right-width: $tb-iris-border-width;
            border-top-color: $tb-iris-neutral-grey-50;
            border-top-style: none;
            border-top-width: $tb-iris-border-width;
            border-top-left-radius: $tb-iris-border-radius;
            border-bottom-left-radius: $tb-iris-border-radius;
        
}
    .k-datetimepicker.k-input{
    border-bottom-style: solid;
            border-left-style: solid;
            border-right-style: solid;
            border-top-style: solid;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-datetimepicker.k-input .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button{
    border-top-right-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            align-self: center;
        
}
    .k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-monthview .k-calendar-table .k-calendar-td.k-today .k-link{
    color: $tb-iris-primary-blue;
        
}
    .k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-monthview .k-calendar-table .k-calendar-td.k-selected .k-link{
    color: $tb-iris-neutral-light-bg;
            background-color: $tb-iris-primary-blue;
            background-image: none;
        
}
    .k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-monthview .k-calendar-table .k-calendar-td.k-disabled .k-link,.k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-monthview .k-calendar-table  .k-calendar-td.k-state-disabled .k-link,.k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-monthview .k-calendar-table  .k-calendar-td:disabled .k-link{
    color: $tb-iris-neutral-grey-70;
        
}
    .k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-monthview .k-calendar-table .k-calendar-td.k-disabled .k-link,.k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-monthview .k-calendar-table .k-calendar-td.k-state-disabled .k-link,.k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-monthview .k-calendar-table .k-calendar-td:disabled .k-link{
    background-color: $tb-iris-neutral-grey-5;
            background-image: none;
        
}
    .k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-yearview .k-calendar-table .k-calendar-td.k-disabled .k-link,.k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-yearview .k-calendar-table .k-calendar-td.k-state-disabled .k-link,.k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-yearview .k-calendar-table .k-calendar-td:disabled .k-link{
    background-color: $tb-iris-neutral-grey-5;
            background-image: none;
        
}
    .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.k-disabled, .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.k-state-disabled, .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input:disabled{
    border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
            background-color: $tb-disabled-background;
            color: $tb-kendo-disabled-text;
            border-bottom-color: $tb-disabled-border;
            border-left-color: $tb-disabled-border;
            border-right-color: $tb-disabled-border;
            border-top-color: $tb-disabled-border;
        
}
    .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.k-disabled,.k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.k-state-disabled,.k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input:disabled{
    background-image: none;
        
}
    .k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-decadeview .k-calendar-table .k-calendar-td.k-disabled .k-link,.k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-decadeview .k-calendar-table .k-calendar-td.k-state-disabled .k-link,.k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-decadeview .k-calendar-table .k-calendar-td:disabled .k-link{
    background-color: $tb-iris-neutral-grey-5;
            background-image: none;
        
}
    .k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-centuryview .k-calendar-table .k-calendar-td.k-disabled .k-link,.k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-centuryview .k-calendar-table .k-calendar-td.k-state-disabled .k-link,.k-calendar.k-calendar-range.k-calendar-md .k-calendar-view.k-calendar-centuryview .k-calendar-table .k-calendar-td:disabled .k-link{
    background-color: $tb-iris-neutral-grey-5;
            background-image: none;
        
}
    .k-listbox .k-list .k-list-content .k-list-ul .k-list-item.k-selected{
    color: $tb-iris-neutral-light-bg;
            background-color: $tb-iris-primary-blue;
            background-image: none;
        
}
    .k-dropdowntree .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down::before{
    content: "\e015";
        
}
    .k-dropdowntree .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down{
    border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-dropdowntree .k-input-button.k-button.k-icon-button{
                    @extend %tb-effects-tb-internal-none-effects;
          border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            border-bottom-left-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-top-left-radius: $tb-iris-border-radius;
            border-top-right-radius: $tb-iris-border-radius;
            background-color: transparent;
            background-image: none;
        
}
    .k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary.k-button{
    border-bottom-left-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-top-left-radius: $tb-iris-border-radius;
            border-top-right-radius: $tb-iris-border-radius;
        
}
    .k-button-group .k-button.k-selected{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
          background-color: $tb-iris-primary-blue;
            background-image: none;
            color: $tb-iris-neutral-light-bg;
            border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
        
}
    .k-notification{
    padding-bottom: 1rem;
            padding-left: 1rem;
            padding-right: 1rem;
            padding-top: 1rem;
            font-family: OpenSans-Variable;
            font-size: 1rem;
            line-height: 1.5;
            border-bottom-color: $tb-iris-neutral-grey-5;
            border-left-color: $tb-iris-neutral-grey-5;
            border-right-color: $tb-iris-neutral-grey-5;
            border-top-color: $tb-iris-neutral-grey-5;
            background-color: $tb-iris-neutral-grey-5;
            background-image: none;
            column-gap: 1rem;
        
}
    .k-notification.k-notification-success .k-notification-status.k-icon.k-font-icon.k-i-check-outline{
    width: 1.5rem;
            height: 1.5rem;
            font-size: 1.5rem;
            color: $tb-iris-secondary-green;
        
}
    .k-notification.k-notification-success .k-notification-status.k-icon.k-font-icon.k-i-check-outline::before{
    content: "\e119";
        
}
    .k-notification.k-notification-success{
    column-gap: 16px;
            color: $tb-iris-primary-grey;
            background-color: k-color( success );
            background-image: none;
            border-bottom-color: k-color( success );
            border-left-color: k-color( success );
            border-right-color: k-color( success );
            border-top-color: k-color( success );
        
}
    .k-notification.k-notification-error{
    column-gap: 1rem;
            color: $tb-iris-primary-grey;
            background-color: $tb-iris-primary-red-10;
            background-image: none;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-notification.k-notification-error .k-notification-status.k-icon.k-font-icon.k-i-x-outline{
    width: 1.5rem;
            height: 1.5rem;
            font-size: 1.5rem;
            color: $tb-iris-primary-red;
        
}
    .k-notification.k-notification-warning .k-notification-status.k-icon.k-font-icon.k-i-exclamation-circle{
    width: 1.5rem;
            height: 1.5rem;
            font-size: 1.5rem;
            color: $tb-iris-primary-orange-dark;
        
}
    .k-notification.k-notification-warning{
    column-gap: 1rem;
            background-color: $tb-iris-primary-orange-10;
            background-image: none;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-notification.k-notification-info{
    column-gap: 1rem;
            font-size: 1rem;
            color: $tb-iris-primary-grey;
            background-color: k-color( info );
            background-image: none;
            border-bottom-color: k-color( info );
            border-left-color: k-color( info );
            border-right-color: k-color( info );
            border-top-color: k-color( info );
        
}
    .k-notification.k-notification-info .k-notification-status.k-icon.k-font-icon.k-i-info-circle{
    width: 1.5rem;
            height: 1.5rem;
            font-size: 1.5rem;
            color: $tb-iris-primary-blue-dark-30;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-disabled .k-link,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item.k-state-disabled .k-link,.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-tabstrip-item:disabled .k-link{
    background-color: $tb-iris-neutral-grey-5;
            background-image: none;
        
}
    .k-button-group .k-button.k-hover,.k-button-group .k-button.k-state-hover,.k-button-group .k-button.k-state-hovered,.k-button-group .k-button:hover{
    border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
        
}
    .k-button-group .k-button.k-hover,.k-button-group  .k-button.k-state-hover,.k-button-group  .k-button.k-state-hovered,.k-button-group  .k-button:hover{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
      
}
    .k-button-group .k-button.k-button-outline.k-active,.k-button-group .k-button.k-button-outline.k-state-active,.k-button-group .k-button.k-button-outline:active{
    color: $tb-iris-primary-grey;
        
}
    .k-button-group .k-button.k-button-outline.k-hover,.k-button-group .k-button.k-button-outline.k-state-hover,.k-button-group .k-button.k-button-outline.k-state-hovered,.k-button-group .k-button.k-button-outline:hover{
    color: $tb-iris-primary-grey;
        
}
    .k-button-group .k-button.k-button-flat.k-selected{
    color: $tb-iris-primary-grey;
        
}
    .k-button-group .k-button{
    margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-button-group .k-button.k-active,.k-button-group .k-button.k-state-active,.k-button-group .k-button:active{
    border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
        
}
    .k-button-group .k-button.k-active,.k-button-group  .k-button.k-state-active,.k-button-group  .k-button:active{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
      
}
    .k-button-group .k-button.k-focus,.k-button-group .k-button.k-state-focus,.k-button-group .k-button.k-state-focused,.k-button-group .k-button:focus{
    border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
        
}
    .k-button-group .k-button.k-focus.k-button-solid-base,.k-button-group .k-button.k-state-focus.k-button-solid-base,.k-button-group .k-button.k-state-focused.k-button-solid-base,.k-button-group .k-button:focus.k-button-solid-base{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
      
}
    .k-reset.k-header.k-menu.k-menu-horizontal .k-item.k-menu-item .k-link.k-menu-link .k-menu-link-text{
    font-size: 14px;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row .k-header.k-table-th .k-cell-inner .k-link,.k-grid.k-grid-md  .k-grid-header .k-table-row .k-header.k-table-th .k-cell-inner .k-link{
    padding-top: 4px;
            padding-bottom: 4px;
        
}
    .k-tabstrip .k-tabstrip-content.k-active,.k-tabstrip  .k-tabstrip-content.k-state-active,.k-tabstrip  .k-tabstrip-content:active{
    border-bottom-color: $tb-iris-neutral-grey-20;
            border-left-color: $tb-iris-neutral-grey-20;
            border-right-color: $tb-iris-neutral-grey-20;
            border-top-color: $tb-iris-neutral-grey-20;
            border-bottom-left-radius: $tb-iris-border-radius;
            border-bottom-right-radius: $tb-iris-border-radius;
            border-top-left-radius: 0px;
            border-top-right-radius: $tb-iris-border-radius;
            outline-style: none;
            margin-top: 0px !important;
            border-bottom-style: solid;
            border-left-style: solid;
            border-right-style: solid;
            border-top-style: solid;
            border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
            padding-bottom: 0px;
            padding-left: 0px;
            padding-right: 0px;
            padding-top: 0px;
        
}
    .k-grid.k-grid-md .k-table .k-table-group-row.k-grouping-row.k-table-row{
    height: 1.5rem;
        
}
    .k-grid.k-grid-md .k-toolbar.k-grid-toolbar.k-toolbar-md{
    padding-bottom: 8px;
            padding-left: 8px;
            padding-right: 8px;
            padding-top: 8px;
            flex-direction: row;
            justify-content: end;
        
}
    .k-card .k-card-body{
    padding-top: 8px;
            padding-bottom: 8px;
            padding-left: 8px;
            padding-right: 8px;
        
}
    .k-datepicker.k-input .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button{
    border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            background-color: initial;
            background-image: none;
            padding-bottom: 0px;
            padding-left: 0px;
            padding-right: 0px;
            padding-top: 0px;
            outline-style: none;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-datepicker.k-input .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button .k-button-icon.k-icon.k-font-icon.k-i-calendar{
    color: $tb-iris-primary-blue;
            display: flex;
        
}
    .k-datepicker.k-input .k-input-inner::placeholder{
    color: $tb-iris-neutral-grey-70;
        
}
    .k-datepicker.k-input.k-hover, .k-datepicker.k-input.k-state-hover, .k-datepicker.k-input.k-state-hovered, .k-datepicker.k-input:hover{
    margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
            padding-bottom: 0px;
            padding-left: 0px;
            padding-right: 0px;
            padding-top: 0px;
        
}
    .k-datepicker.k-input.k-invalid, .k-datepicker.k-input.ng-invalid.ng-touched, .k-datepicker.k-input.ng-invalid.ng-dirty{
    border-bottom-color: $tb-invalid;
            border-left-color: $tb-invalid;
            border-right-color: $tb-invalid;
            border-top-color: $tb-invalid;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-datepicker.k-input.k-disabled, .k-datepicker.k-input.k-state-disabled, .k-datepicker.k-input:disabled{
    border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
            background-image: none;
            border-bottom-color: $tb-disabled-border;
            border-left-color: $tb-disabled-border;
            border-right-color: $tb-disabled-border;
            border-top-color: $tb-disabled-border;
        
}
    .k-datepicker.k-input.k-disabled.k-input-solid, .k-datepicker.k-input.k-state-disabled.k-input-solid, .k-datepicker.k-input:disabled.k-input-solid{
    background-color: $tb-disabled-background;
        
}
    .k-datepicker.k-input.k-state-focus-within, .k-datepicker.k-input:focus-within{
    margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-datepicker.k-input.k-state-focus-within.k-input-solid, .k-datepicker.k-input:focus-within.k-input-solid{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-datepicker.k-input.k-invalid.k-state-focus-within, .k-datepicker.k-input.ng-invalid.ng-touched.k-state-focus-within, .k-datepicker.k-input.ng-invalid.ng-dirty.k-state-focus-within, .k-datepicker.k-input.k-invalid:focus-within, .k-datepicker.k-input.ng-invalid.ng-touched:focus-within, .k-datepicker.k-input.ng-invalid.ng-dirty:focus-within{
    outline-style: none;
        
}
    .k-datepicker.k-input.k-invalid.k-state-focus-within.k-input-solid, .k-datepicker.k-input.ng-invalid.ng-touched.k-state-focus-within.k-input-solid, .k-datepicker.k-input.ng-invalid.ng-dirty.k-state-focus-within.k-input-solid, .k-datepicker.k-input.k-invalid:focus-within.k-input-solid, .k-datepicker.k-input.ng-invalid.ng-touched:focus-within.k-input-solid, .k-datepicker.k-input.ng-invalid.ng-dirty:focus-within.k-input-solid{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-datepicker.k-input.k-hover .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button, .k-datepicker.k-input.k-state-hover .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button, .k-datepicker.k-input.k-state-hovered .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button, .k-datepicker.k-input:hover .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button{
    margin-right: 0px;
            height: $tb-iris-form-input-height;
        
}
    .k-datepicker.k-input.k-hover .k-input-inner, .k-datepicker.k-input.k-state-hover .k-input-inner, .k-datepicker.k-input.k-state-hovered .k-input-inner, .k-datepicker.k-input:hover .k-input-inner{
    margin-left: 0px;
        
}
    .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.k-focus, .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.k-state-focus, .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.k-state-focused, .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input:focus{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.k-invalid .k-input-inner, .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.ng-invalid.ng-touched .k-input-inner, .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.ng-invalid.ng-dirty .k-input-inner{
    border-bottom-color: $tb-invalid;
            border-bottom-style: solid;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-color: $tb-invalid;
            border-left-style: solid;
            border-left-width: $tb-iris-selected-border-width;
            border-right-color: $tb-invalid;
            border-right-style: solid;
            border-right-width: $tb-iris-selected-border-width;
            border-top-color: $tb-invalid;
            border-top-style: solid;
            border-top-width: $tb-iris-selected-border-width;
            color: $tb-invalid;
        
}
    .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input .k-input-inner.k-state-readonly,.k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input  .k-input-inner[readonly]{
    background-color: $tb-iris-neutral-grey-5;
            background-image: none;
        
}
    .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.k-disabled .k-input-suffix.k-input-suffix-horizontal, .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.k-state-disabled .k-input-suffix.k-input-suffix-horizontal, .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input:disabled .k-input-suffix.k-input-suffix-horizontal{
    color: $tb-kendo-disabled-text;
        
}
    .k-dropdownlist.k-hover .k-input-button, .k-dropdownlist.k-state-hover .k-input-button, .k-dropdownlist.k-state-hovered .k-input-button, .k-dropdownlist:hover .k-input-button{
    border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            background-color: transparent;
            background-image: none;
        
}
    .k-dropdownlist.k-hover .k-input-button.k-button, .k-dropdownlist.k-state-hover .k-input-button.k-button, .k-dropdownlist.k-state-hovered .k-input-button.k-button, .k-dropdownlist:hover .k-input-button.k-button{
    border-top-style: none;
            justify-content: center;
        
}
    .k-dropdownlist.k-hover .k-input-inner, .k-dropdownlist.k-state-hover .k-input-inner, .k-dropdownlist.k-state-hovered .k-input-inner, .k-dropdownlist:hover .k-input-inner{
    outline-style: none;
        
}
    .k-dropdownlist.k-disabled,.k-dropdownlist.k-state-disabled,.k-dropdownlist:disabled{
    border-bottom-color: $tb-disabled-border;
            border-left-color: $tb-disabled-border;
            border-right-color: $tb-disabled-border;
            border-top-color: $tb-disabled-border;
            border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
            background-color: $tb-disabled-background;
            background-image: none;
        
}
    .k-dropdownlist.k-disabled .k-input-button, .k-dropdownlist.k-state-disabled .k-input-button, .k-dropdownlist:disabled .k-input-button{
    background-color: $tb-disabled-background;
            background-image: none;
            color: $tb-disabled-text;
        
}
    .k-dropdownlist.k-disabled .k-input-inner, .k-dropdownlist.k-state-disabled .k-input-inner, .k-dropdownlist:disabled .k-input-inner{
    color: $tb-disabled-text;
        
}
    .k-dropdownlist.k-hover .k-input-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdownlist.k-state-hover .k-input-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdownlist.k-state-hovered .k-input-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdownlist:hover .k-input-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down{
    display: flex;
            justify-content: center;
        
}
    .k-dropdownlist .k-input-inner .k-input-value-text{
    opacity: inherit;
        
}
    .k-dropdownlist .k-input-inner .k-input-value-text::before{
    width: 0px;
        
}
    .k-numerictextbox.k-state-focus-within.k-input-solid, .k-numerictextbox:focus-within.k-input-solid{
    box-shadow: none;
        
}
    .k-numerictextbox.k-disabled .k-input-inner, .k-numerictextbox.k-state-disabled .k-input-inner, .k-numerictextbox:disabled .k-input-inner{
    background-color: $tb-disabled-background;
            background-image: none;
        
}
    .k-numerictextbox.k-disabled .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-up, .k-numerictextbox.k-state-disabled .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-up, .k-numerictextbox:disabled .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-up{
    color: $tb-disabled-text;
        
}
    .k-numerictextbox.k-disabled .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-numerictextbox.k-state-disabled .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-numerictextbox:disabled .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down{
    color: $tb-disabled-text;
        
}
    .k-numerictextbox.k-invalid .k-input-inner, .k-numerictextbox.ng-invalid.ng-touched .k-input-inner, .k-numerictextbox.ng-invalid.ng-dirty .k-input-inner{
    border-bottom-color: $tb-invalid;
            border-left-color: $tb-invalid;
            border-right-color: $tb-invalid;
            border-top-color: $tb-invalid;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
            color: $tb-invalid;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-timepicker.k-input.k-state-focus-within.k-input-solid, .k-timepicker.k-input:focus-within.k-input-solid{
    box-shadow: none;
        
}
    .k-timepicker.k-input.k-invalid, .k-timepicker.k-input.ng-invalid.ng-touched, .k-timepicker.k-input.ng-invalid.ng-dirty{
    border-bottom-color: $tb-invalid;
            border-left-color: $tb-invalid;
            border-right-color: $tb-invalid;
            border-top-color: $tb-invalid;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-timepicker.k-input.k-invalid .k-input-inner, .k-timepicker.k-input.ng-invalid.ng-touched .k-input-inner, .k-timepicker.k-input.ng-invalid.ng-dirty .k-input-inner{
    color: $tb-invalid;
        
}
    .k-timepicker.k-input.k-invalid .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button, .k-timepicker.k-input.ng-invalid.ng-touched .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button, .k-timepicker.k-input.ng-invalid.ng-dirty .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button{
    align-self: center;
        
}
    .k-timepicker.k-input.k-disabled.k-input-solid, .k-timepicker.k-input.k-state-disabled.k-input-solid, .k-timepicker.k-input:disabled.k-input-solid{
    background-color: $tb-iris-neutral-grey-5;
        
}
    .k-timepicker.k-input.k-disabled, .k-timepicker.k-input.k-state-disabled, .k-timepicker.k-input:disabled{
    background-image: none;
            border-bottom-color: $tb-disabled-border;
            border-left-color: $tb-disabled-border;
            border-right-color: $tb-disabled-border;
            border-top-color: $tb-disabled-border;
        
}
    .k-timepicker.k-input.k-disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button .k-button-icon.k-icon.k-font-icon.k-i-clock, .k-timepicker.k-input.k-state-disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button .k-button-icon.k-icon.k-font-icon.k-i-clock, .k-timepicker.k-input:disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button .k-button-icon.k-icon.k-font-icon.k-i-clock{
    color: $tb-disabled-text;
        
}
    .k-datetimepicker.k-input.k-state-focus-within.k-input-solid, .k-datetimepicker.k-input:focus-within.k-input-solid{
    box-shadow: none;
        
}
    .k-datetimepicker.k-input.k-invalid .k-input-inner, .k-datetimepicker.k-input.ng-invalid.ng-touched .k-input-inner, .k-datetimepicker.k-input.ng-invalid.ng-dirty .k-input-inner{
    color: $tb-invalid;
        
}
    .k-datetimepicker.k-input.k-invalid, .k-datetimepicker.k-input.ng-invalid.ng-touched, .k-datetimepicker.k-input.ng-invalid.ng-dirty{
    border-bottom-color: $tb-invalid;
            border-left-color: $tb-invalid;
            border-right-color: $tb-invalid;
            border-top-color: $tb-invalid;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-datetimepicker.k-input.k-disabled, .k-datetimepicker.k-input.k-state-disabled, .k-datetimepicker.k-input:disabled{
    border-bottom-color: $tb-disabled-border;
            border-left-color: $tb-disabled-border;
            border-right-color: $tb-disabled-border;
            border-top-color: $tb-disabled-border;
            background-image: none;
        
}
    .k-datetimepicker.k-input.k-disabled.k-input-solid, .k-datetimepicker.k-input.k-state-disabled.k-input-solid, .k-datetimepicker.k-input:disabled.k-input-solid{
    background-color: $tb-disabled-background;
        
}
    .k-datetimepicker.k-input.k-disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button .k-button-icon.k-icon.k-font-icon.k-i-calendar, .k-datetimepicker.k-input.k-state-disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button .k-button-icon.k-icon.k-font-icon.k-i-calendar, .k-datetimepicker.k-input:disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button .k-button-icon.k-icon.k-font-icon.k-i-calendar{
    color: $tb-disabled-text;
        
}
    .k-datepicker.k-input.k-invalid .k-input-inner, .k-datepicker.k-input.ng-invalid.ng-touched .k-input-inner, .k-datepicker.k-input.ng-invalid.ng-dirty .k-input-inner{
    color: $tb-invalid;
        
}
    .k-datepicker.k-input.k-disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button .k-button-icon.k-icon.k-font-icon.k-i-calendar, .k-datepicker.k-input.k-state-disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button .k-button-icon.k-icon.k-font-icon.k-i-calendar, .k-datepicker.k-input:disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button .k-button-icon.k-icon.k-font-icon.k-i-calendar{
    color: $tb-disabled-text;
        
}
    .k-combobox.k-input.k-disabled, .k-combobox.k-input.k-state-disabled, .k-combobox.k-input:disabled{
    border-bottom-color: $tb-disabled-border;
            border-left-color: $tb-disabled-border;
            border-right-color: $tb-disabled-border;
            border-top-color: $tb-disabled-border;
            background-image: none;
        
}
    .k-combobox.k-input.k-disabled.k-input-solid, .k-combobox.k-input.k-state-disabled.k-input-solid, .k-combobox.k-input:disabled.k-input-solid{
    background-color: $tb-disabled-background;
        
}
    .k-combobox.k-input.k-disabled .k-input-button,.k-combobox.k-input.k-state-disabled .k-input-button,.k-combobox.k-input:disabled .k-input-button{
    color: $tb-disabled-text;
        
}
    .k-combobox.k-input.k-state-focus-within, .k-combobox.k-input:focus-within{
    border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
        
}
    .k-combobox.k-input.k-state-focus-within.k-input-solid, .k-combobox.k-input:focus-within.k-input-solid{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
      
}
    .k-combobox.k-input.k-invalid .k-input-inner, .k-combobox.k-input.ng-invalid.ng-touched .k-input-inner, .k-combobox.k-input.ng-invalid.ng-dirty .k-input-inner{
    color: $tb-invalid;
        
}
    .k-combobox.k-input.k-invalid, .k-combobox.k-input.ng-invalid.ng-touched, .k-combobox.k-input.ng-invalid.ng-dirty{
                    @extend %tb-effects-tb-internal-none-effects;
          border-bottom-color: $tb-invalid;
            border-left-color: $tb-invalid;
            border-right-color: $tb-invalid;
            border-top-color: $tb-invalid;
            border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-combobox.k-input.k-invalid .k-input-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox.k-input.ng-invalid.ng-touched .k-input-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox.k-input.ng-invalid.ng-dirty .k-input-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down{
    align-self: center;
        
}
    .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.k-invalid, .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.ng-invalid.ng-touched, .k-input-md.k-rounded-md.k-input-solid.k-textbox.k-input.ng-invalid.ng-dirty{
    margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-daterangepicker.k-disabled .k-floating-label-container .k-dateinput.k-disabled,.k-daterangepicker.k-disabled .k-floating-label-container  .k-dateinput.k-state-disabled,.k-daterangepicker.k-disabled .k-floating-label-container  .k-dateinput:disabled, .k-daterangepicker.k-state-disabled .k-floating-label-container .k-dateinput.k-disabled, .k-daterangepicker.k-state-disabled .k-floating-label-container  .k-dateinput.k-state-disabled, .k-daterangepicker.k-state-disabled .k-floating-label-container  .k-dateinput:disabled, .k-daterangepicker:disabled .k-floating-label-container .k-dateinput.k-disabled, .k-daterangepicker:disabled .k-floating-label-container  .k-dateinput.k-state-disabled, .k-daterangepicker:disabled .k-floating-label-container  .k-dateinput:disabled{
    border-bottom-color: $tb-disabled-border;
            border-left-color: $tb-disabled-border;
            border-right-color: $tb-disabled-border;
            border-top-color: $tb-disabled-border;
            background-image: none;
        
}
    .k-daterangepicker.k-disabled .k-floating-label-container .k-dateinput.k-disabled.k-input-solid,.k-daterangepicker.k-disabled .k-floating-label-container  .k-dateinput.k-state-disabled.k-input-solid,.k-daterangepicker.k-disabled .k-floating-label-container  .k-dateinput:disabled.k-input-solid, .k-daterangepicker.k-state-disabled .k-floating-label-container .k-dateinput.k-disabled.k-input-solid, .k-daterangepicker.k-state-disabled .k-floating-label-container  .k-dateinput.k-state-disabled.k-input-solid, .k-daterangepicker.k-state-disabled .k-floating-label-container  .k-dateinput:disabled.k-input-solid, .k-daterangepicker:disabled .k-floating-label-container .k-dateinput.k-disabled.k-input-solid, .k-daterangepicker:disabled .k-floating-label-container  .k-dateinput.k-state-disabled.k-input-solid, .k-daterangepicker:disabled .k-floating-label-container  .k-dateinput:disabled.k-input-solid{
    background-color: $tb-disabled-background;
            color: $tb-disabled-text;
        
}
    .k-daterangepicker .k-floating-label-container.k-focus .k-dateinput.k-state-focus-within.k-input-solid,.k-daterangepicker .k-floating-label-container.k-focus  .k-dateinput:focus-within.k-input-solid,.k-daterangepicker  .k-floating-label-container.k-state-focus .k-dateinput.k-state-focus-within.k-input-solid,.k-daterangepicker  .k-floating-label-container.k-state-focus  .k-dateinput:focus-within.k-input-solid,.k-daterangepicker  .k-floating-label-container.k-state-focused .k-dateinput.k-state-focus-within.k-input-solid,.k-daterangepicker  .k-floating-label-container.k-state-focused  .k-dateinput:focus-within.k-input-solid,.k-daterangepicker  .k-floating-label-container:focus .k-dateinput.k-state-focus-within.k-input-solid,.k-daterangepicker  .k-floating-label-container:focus  .k-dateinput:focus-within.k-input-solid{
    box-shadow: none;
        
}
    .k-daterangepicker .k-floating-label-container .k-dateinput.k-invalid,.k-daterangepicker .k-floating-label-container  .k-dateinput.ng-invalid.ng-touched,.k-daterangepicker .k-floating-label-container  .k-dateinput.ng-invalid.ng-dirty{
    border-bottom-color: $tb-invalid;
            border-left-color: $tb-invalid;
            border-right-color: $tb-invalid;
            border-top-color: $tb-invalid;
        
}
    .k-daterangepicker .k-floating-label-container .k-dateinput.k-invalid.k-input,.k-daterangepicker .k-floating-label-container  .k-dateinput.ng-invalid.ng-touched.k-input,.k-daterangepicker .k-floating-label-container  .k-dateinput.ng-invalid.ng-dirty.k-input{
    border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
            margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
        
}
    .k-daterangepicker .k-floating-label-container .k-dateinput.k-invalid.k-input-solid,.k-daterangepicker .k-floating-label-container  .k-dateinput.ng-invalid.ng-touched.k-input-solid,.k-daterangepicker .k-floating-label-container  .k-dateinput.ng-invalid.ng-dirty.k-input-solid{
    color: $tb-invalid;
        
}
    .k-daterangepicker .k-floating-label-container .k-dateinput.k-input{
    margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-daterangepicker .k-floating-label-container .k-dateinput.k-invalid .k-input-inner,.k-daterangepicker .k-floating-label-container  .k-dateinput.ng-invalid.ng-touched .k-input-inner,.k-daterangepicker .k-floating-label-container  .k-dateinput.ng-invalid.ng-dirty .k-input-inner{
    align-self: center;
        
}
    .k-dropdownlist.k-invalid.k-focus .k-input-inner, .k-dropdownlist.ng-invalid.ng-touched.k-focus .k-input-inner, .k-dropdownlist.ng-invalid.ng-dirty.k-focus .k-input-inner, .k-dropdownlist.k-invalid.k-state-focus .k-input-inner, .k-dropdownlist.k-invalid.k-state-focused .k-input-inner, .k-dropdownlist.k-invalid:focus .k-input-inner, .k-dropdownlist.ng-invalid.ng-touched.k-state-focus .k-input-inner, .k-dropdownlist.ng-invalid.ng-touched.k-state-focused .k-input-inner, .k-dropdownlist.ng-invalid.ng-touched:focus .k-input-inner, .k-dropdownlist.ng-invalid.ng-dirty.k-state-focus .k-input-inner, .k-dropdownlist.ng-invalid.ng-dirty.k-state-focused .k-input-inner, .k-dropdownlist.ng-invalid.ng-dirty:focus .k-input-inner{
    color: $tb-invalid;
        
}
    .k-dropdownlist-popup .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-selected{
    background-color: initial;
            background-image: none;
        
}
    .k-menu-popup .k-group.k-menu-group .k-item.k-menu-item .k-link.k-menu-link.k-active,.k-menu-popup .k-group.k-menu-group .k-item.k-menu-item .k-link.k-menu-link.k-state-active,.k-menu-popup .k-group.k-menu-group .k-item.k-menu-item .k-link.k-menu-link:active{
    background-color: initial;
            background-image: none;
            color: inherit;
        
}
    .k-tabstrip .k-tabstrip-items-wrapper.k-hstack .k-tabstrip-items.k-reset{
    row-gap: 2px;
            column-gap: 2px;
            border-bottom-color: initial;
            border-bottom-style: none;
            border-bottom-width: 0px;
        
}
    .k-grid.k-grid-md .k-table-tbody .k-master-row.k-table-row td.k-focus,.k-grid.k-grid-md .k-table-tbody .k-master-row.k-table-row  td.k-state-focus,.k-grid.k-grid-md .k-table-tbody .k-master-row.k-table-row  td.k-state-focused,.k-grid.k-grid-md .k-table-tbody .k-master-row.k-table-row  td:focus{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-grid.k-grid-md .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base .k-icon.k-font-icon.k-i-filter.k-button-icon,.k-grid.k-grid-md .k-grid-header .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base .k-icon.k-font-icon.k-i-filter.k-button-icon{
    left: -8px;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base .k-icon.k-font-icon.k-i-filter.k-button-icon::before,.k-grid.k-grid-md  .k-grid-header .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base .k-icon.k-font-icon.k-i-filter.k-button-icon::before{
    content: "\e0cb";
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base .k-icon.k-font-icon.k-i-filter.k-button-icon,.k-grid.k-grid-md  .k-grid-header .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base .k-icon.k-font-icon.k-i-filter.k-button-icon{
                    @extend %tb-typography-Iris-IFM-font-icons;
      
}
    .k-numerictextbox .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease.k-hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down,.k-numerictextbox .k-input-spinner.k-spin-button  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease.k-state-hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down,.k-numerictextbox .k-input-spinner.k-spin-button  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease.k-state-hovered .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down,.k-numerictextbox .k-input-spinner.k-spin-button  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease:hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down{
    top: -1px;
            display: flex;
        
}
    .k-numerictextbox .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase.k-hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-up,.k-numerictextbox .k-input-spinner.k-spin-button  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase.k-state-hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-up,.k-numerictextbox .k-input-spinner.k-spin-button  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase.k-state-hovered .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-up,.k-numerictextbox .k-input-spinner.k-spin-button  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase:hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-up{
    bottom: -1px;
            display: flex;
        
}
    .k-numerictextbox.k-invalid.k-state-focus-within, .k-numerictextbox.ng-invalid.ng-touched.k-state-focus-within, .k-numerictextbox.ng-invalid.ng-dirty.k-state-focus-within, .k-numerictextbox.k-invalid:focus-within, .k-numerictextbox.ng-invalid.ng-touched:focus-within, .k-numerictextbox.ng-invalid.ng-dirty:focus-within{
    border-bottom-color: $tb-invalid;
            border-left-color: $tb-invalid;
            border-right-color: $tb-invalid;
            border-top-color: $tb-invalid;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
        
}
    .k-numerictextbox.k-invalid, .k-numerictextbox.ng-invalid.ng-touched, .k-numerictextbox.ng-invalid.ng-dirty{
    margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
            border-bottom-color: $tb-invalid;
            border-left-color: $tb-invalid;
            border-right-color: $tb-invalid;
            border-top-color: $tb-invalid;
        
}
    .k-numerictextbox.k-disabled.k-input-solid, .k-numerictextbox.k-state-disabled.k-input-solid, .k-numerictextbox:disabled.k-input-solid{
    background-color: $tb-disabled-background;
        
}
    .k-numerictextbox.k-disabled, .k-numerictextbox.k-state-disabled, .k-numerictextbox:disabled{
    background-image: none;
            border-bottom-color: $tb-disabled-border;
            border-left-color: $tb-disabled-border;
            border-right-color: $tb-disabled-border;
            border-top-color: $tb-disabled-border;
            border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
        
}
    .k-numerictextbox.k-invalid .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-numerictextbox.ng-invalid.ng-touched .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-numerictextbox.ng-invalid.ng-dirty .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-decrease .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down{
    top: -2px;
        
}
    .k-numerictextbox.k-invalid .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-up, .k-numerictextbox.ng-invalid.ng-touched .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-up, .k-numerictextbox.ng-invalid.ng-dirty .k-input-spinner.k-spin-button .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-spinner-increase .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-up{
    bottom: 0px;
        
}
    .k-list-filter .k-searchbox.k-invalid .k-input-inner,.k-list-filter  .k-searchbox.ng-invalid.ng-touched .k-input-inner,.k-list-filter  .k-searchbox.ng-invalid.ng-dirty .k-input-inner{
    border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button.k-disabled,.k-grid.k-grid-md .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button.k-state-disabled,.k-grid.k-grid-md .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button:disabled,.k-grid.k-grid-md .k-grid-header .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button.k-disabled,.k-grid.k-grid-md .k-grid-header .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button.k-state-disabled,.k-grid.k-grid-md .k-grid-header .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button:disabled{
    width: 32px;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base,.k-grid.k-grid-md .k-grid-header .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base{
    border-bottom-left-radius: 0px;
            border-bottom-right-radius: 0px;
            border-top-left-radius: 0px;
            border-top-right-radius: 0px;
            height: $tb-iris-form-input-height;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base,.k-grid.k-grid-md  .k-grid-header .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base{
    background-color: transparent;
            background-image: none;
            width: $tb-iris-form-input-height;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator,.k-grid.k-grid-md  .k-grid-header .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator{
    width: 32px;
            height: 32px;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base .k-icon.k-svg-icon.k-button-icon,.k-grid.k-grid-md .k-grid-header .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base .k-icon.k-svg-icon.k-button-icon{
    margin-left: -16px;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base .k-icon.k-svg-icon.k-button-icon >svg,.k-grid.k-grid-md  .k-grid-header .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-filtercell-operator .k-dropdownlist.k-picker.k-dropdown-operator.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base .k-icon.k-svg-icon.k-button-icon >svg{
    box-sizing: border-box;
        
}
    .k-pager .k-pager-sizes{
    column-gap: 8px;
        
}
    .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-hover,.k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-state-hover,.k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-state-hovered,.k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button:hover{
                    @extend %tb-effects-iris-standard-inner-shadow;
          color: $tb-iris-primary-blue-dark-30;
            background-color: initial;
            background-image: none;
            border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
        
}
    .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-active,.k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-state-active,.k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button:active{
    color: k-color( on-dark );
            background-color: $tb-iris-primary-blue-dark-30;
            background-image: none;
        
}
    .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button{
    color: $tb-iris-primary-blue;
        
}
    .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-selected{
    color: k-color( on-dark );
            background-color: $tb-iris-primary-blue-dark-30;
            background-image: none;
        
}
    .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-focus, .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-state-focus, .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-state-focused, .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button:focus{
    color: $tb-iris-primary-blue-dark-30;
        
}
    .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-focus,.k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-state-focus,.k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-state-focused,.k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button:focus{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
      
}
    .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-disabled, .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-state-disabled, .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button:disabled{
    background-color: $tb-disabled-background;
            background-image: none;
            color: $tb-disabled-text;
        
}
    .k-radio-wrap .k-radio{
    background-color: $tb-iris-neutral-light-bg;
            background-image: none;
            border-bottom-color: $tb-iris-primary-blue;
            border-left-color: $tb-iris-primary-blue;
            border-right-color: $tb-iris-primary-blue;
            border-top-color: $tb-iris-primary-blue;
        
}
    .k-radio-wrap .k-radio.k-hover,.k-radio-wrap  .k-radio.k-state-hover,.k-radio-wrap  .k-radio.k-state-hovered,.k-radio-wrap  .k-radio:hover{
    border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
        
}
    .k-radio-wrap .k-radio.k-focus,.k-radio-wrap  .k-radio.k-state-focus,.k-radio-wrap  .k-radio.k-state-focused,.k-radio-wrap  .k-radio:focus{
    border-bottom-color: $tb-iris-primary-blue;
            border-left-color: $tb-iris-primary-blue;
            border-right-color: $tb-iris-primary-blue;
            border-top-color: $tb-iris-primary-blue;
        
}
    .k-radio-wrap .k-radio.k-invalid,.k-radio-wrap  .k-radio.ng-invalid.ng-touched,.k-radio-wrap  .k-radio.ng-invalid.ng-dirty{
    border-bottom-color: $tb-invalid;
            border-left-color: $tb-invalid;
            border-right-color: $tb-invalid;
            border-top-color: $tb-invalid;
        
}
    .k-radio-wrap .k-radio.k-disabled,.k-radio-wrap  .k-radio.k-state-disabled,.k-radio-wrap  .k-radio:disabled{
    border-bottom-color: $tb-disabled-border;
            border-left-color: $tb-disabled-border;
            border-right-color: $tb-disabled-border;
            border-top-color: $tb-disabled-border;
            border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
            background-color: $tb-disabled-background;
        
}
    .k-radio-wrap .k-radio.k-disabled,.k-radio-wrap .k-radio.k-state-disabled,.k-radio-wrap .k-radio:disabled{
    background-image: none;
        
}
    .k-radio-wrap .k-radio.k-checked,.k-radio-wrap  .k-radio:checked{
    background-color: $tb-iris-neutral-light-bg;
            background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2014%2014'%3E%3Ccircle%20cx%3D'50%25'%20cy%3D'50%25'%20r%3D'4'%20fill%3D'%231B69b9'%2F%3E%3C%2Fsvg%3E");
        
}
    .k-radio-wrap .k-radio.k-checked.k-hover,.k-radio-wrap  .k-radio:checked.k-hover,.k-radio-wrap  .k-radio.k-checked.k-state-hover,.k-radio-wrap  .k-radio.k-checked.k-state-hovered,.k-radio-wrap  .k-radio.k-checked:hover,.k-radio-wrap  .k-radio:checked.k-state-hover,.k-radio-wrap  .k-radio:checked.k-state-hovered,.k-radio-wrap  .k-radio:checked:hover{
    background-color: $tb-iris-neutral-light-bg;
            background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2014%2014'%3E%3Ccircle%20cx%3D'50%25'%20cy%3D'50%25'%20r%3D'4'%20fill%3D'%23134A82'%2F%3E%3C%2Fsvg%3E");
        
}
    .k-radio-wrap .k-radio.k-checked.k-disabled,.k-radio-wrap  .k-radio:checked.k-disabled,.k-radio-wrap  .k-radio.k-checked.k-state-disabled,.k-radio-wrap  .k-radio.k-checked:disabled,.k-radio-wrap  .k-radio:checked.k-state-disabled,.k-radio-wrap  .k-radio:checked:disabled{
    background-color: $tb-disabled-background;
            background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2014%2014'%3E%3Ccircle%20cx%3D'50%25'%20cy%3D'50%25'%20r%3D'4'%20fill%3D'%23666E76'%2F%3E%3C%2Fsvg%3E");
        
}
    .k-radio-label{
    padding-right: $tb-iris-button-gap;
        
}
    .k-switch.k-switch-off .k-switch-thumb-wrap .k-switch-thumb{
    width: 12px;
            height: 12px;
            background-color: $tb-iris-neutral-grey-50;
            background-image: none;
        
}
    .k-switch.k-switch-off .k-switch-track{
    border-bottom-color: $tb-iris-neutral-grey-50;
            border-left-color: $tb-iris-neutral-grey-50;
            border-right-color: $tb-iris-neutral-grey-50;
            border-top-color: $tb-iris-neutral-grey-50;
            height: 24px;
            width: 42px;
        
}
    .k-switch.k-switch-off.k-disabled .k-switch-track, .k-switch.k-switch-off.k-state-disabled .k-switch-track, .k-switch.k-switch-off:disabled .k-switch-track{
    border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
            border-bottom-color: $tb-disabled-border;
            border-left-color: $tb-disabled-border;
            border-right-color: $tb-disabled-border;
            border-top-color: $tb-disabled-border;
            background-color: $tb-disabled-background;
            background-image: none;
            color: $tb-disabled-text;
        
}
    .k-switch.k-switch-on.k-disabled .k-switch-track, .k-switch.k-switch-on.k-state-disabled .k-switch-track, .k-switch.k-switch-on:disabled .k-switch-track{
    color: $tb-disabled-text;
            background-color: $tb-disabled-background;
            background-image: none;
            border-bottom-color: $tb-disabled-border;
            border-left-color: $tb-disabled-border;
            border-right-color: $tb-disabled-border;
            border-top-color: $tb-disabled-border;
            border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
        
}
    .k-switch.k-switch-on.k-disabled .k-switch-thumb-wrap .k-switch-thumb, .k-switch.k-switch-on.k-state-disabled .k-switch-thumb-wrap .k-switch-thumb, .k-switch.k-switch-on:disabled .k-switch-thumb-wrap .k-switch-thumb{
    background-color: $tb-iris-neutral-grey-10;
            background-image: none;
        
}
    .k-switch.k-switch-on .k-switch-thumb-wrap .k-switch-thumb{
    width: 12px;
            height: 12px;
        
}
    .k-switch.k-switch-off{
    width: 52px;
            height: 24px;
        
}
    .k-switch.k-switch-on{
    width: 52px;
            height: 24px;
        
}
    .k-switch.k-switch-on .k-switch-track{
    width: 52px;
            height: 24px;
        
}
    .k-switch.k-switch-off.k-hover .k-switch-track, .k-switch.k-switch-off.k-state-hover .k-switch-track, .k-switch.k-switch-off.k-state-hovered .k-switch-track, .k-switch.k-switch-off:hover .k-switch-track{
    border-bottom-color: $tb-iris-primary-blue;
            border-left-color: $tb-iris-primary-blue;
            border-right-color: $tb-iris-primary-blue;
            border-top-color: $tb-iris-primary-blue;
            border-bottom-width: $tb-iris-selected-border-width;
            border-left-width: $tb-iris-selected-border-width;
            border-right-width: $tb-iris-selected-border-width;
            border-top-width: $tb-iris-selected-border-width;
        
}
    .k-switch.k-switch-off.k-focus .k-switch-track, .k-switch.k-switch-off.k-state-focus .k-switch-track, .k-switch.k-switch-off.k-state-focused .k-switch-track, .k-switch.k-switch-off:focus .k-switch-track{
                    @extend %tb-effects-iris-standard-drop-shadow;
          outline-style: none;
        
}
    .k-switch.k-switch-on.k-hover .k-switch-track, .k-switch.k-switch-on.k-state-hover .k-switch-track, .k-switch.k-switch-on.k-state-hovered .k-switch-track, .k-switch.k-switch-on:hover .k-switch-track{
    background-color: $tb-iris-primary-blue-dark-30;
            background-image: none;
        
}
    .k-switch.k-switch-on.k-focus .k-switch-track, .k-switch.k-switch-on.k-state-focus .k-switch-track, .k-switch.k-switch-on.k-state-focused .k-switch-track, .k-switch.k-switch-on:focus .k-switch-track{
    background-color: $tb-iris-primary-blue-dark-30;
            background-image: none;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button.k-clear-button-visible,.k-grid.k-grid-md  .k-grid-header .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button.k-clear-button-visible{
    width: 30px;
        
}
    .k-grid.k-grid-md .k-grid-header .k-grid-header-wrap .k-table.k-grid-header-table .k-table-thead .k-table-row.k-filter-row .k-table-th .k-filtercell .k-filtercell-wrapper .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-icon-button.k-clear-button-visible{
    height: 30px;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar{
    background-color: $tb-iris-neutral-light-bg;
            background-image: none;
            color: $tb-iris-primary-blue;
            border-bottom-color: $tb-iris-neutral-grey-20;
            border-bottom-style: solid;
            border-bottom-width: 0px;
            border-left-color: $tb-iris-neutral-grey-20;
            border-left-style: solid;
            border-right-color: $tb-iris-neutral-grey-20;
            border-right-style: solid;
            border-top-color: $tb-iris-neutral-grey-20;
            border-top-style: solid;
            border-top-width: $tb-iris-border-width;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-title.k-dialog-title{
    color: $tb-iris-primary-grey;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-hover,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-hover,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-hovered,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button:hover{
    border-bottom-style: solid;
            border-left-style: solid;
            border-right-style: solid;
            border-top-style: solid;
            margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
            border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
            color: $tb-iris-primary-blue-dark-30;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-focus::after,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-focus::after,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-focused::after,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button:focus::after{
    box-shadow: none;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-focus .k-button-icon.k-icon.k-font-icon.k-i-x{
                    @extend %tb-effects-iris-standard-drop-shadow;
      
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-active::before,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-active::before,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button:active::before{
    background-color: initial;
            background-image: none;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-hover::before,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-hover::before,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-hovered::before,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button:hover::before{
    background-color: initial;
            background-image: none;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-hover .k-button-icon.k-icon.k-font-icon.k-i-x,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-hover .k-button-icon.k-icon.k-font-icon.k-i-x,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-hovered .k-button-icon.k-icon.k-font-icon.k-i-x,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button:hover .k-button-icon.k-icon.k-font-icon.k-i-x{
    color: $tb-iris-primary-blue-dark-30;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-active .k-button-icon.k-icon.k-font-icon.k-i-x,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-active .k-button-icon.k-icon.k-font-icon.k-i-x,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button:active .k-button-icon.k-icon.k-font-icon.k-i-x{
    color: $tb-iris-primary-blue-dark-30;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-focus::before,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-focus::before,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-focused::before,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button:focus::before{
    background-color: initial;
            background-image: none;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-active::after,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-active::after,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button:active::after{
    box-shadow: none;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-hover::after,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-hover::after,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-hovered::after,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button:hover::after{
    box-shadow: none;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button::after{
    box-shadow: none;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button::before{
    background-color: initial;
            background-image: none;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-x{
    width: 16px;
            height: 16px;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-active,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-active,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button:active{
    margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
            border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
            color: $tb-iris-primary-blue-dark-30;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-focus,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-focus,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button.k-state-focused,.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions  .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button:focus{
    margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
            border-bottom-width: $tb-iris-border-width;
            border-left-width: $tb-iris-border-width;
            border-right-width: $tb-iris-border-width;
            border-top-width: $tb-iris-border-width;
        
}
    .k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar .k-window-titlebar-actions.k-dialog-titlebar-actions .k-window-titlebar-action.k-dialog-titlebar-action.k-button.k-button-md.k-button-flat.k-button-flat-base.k-rounded-md.k-icon-button{
    border-bottom-style: solid;
            border-left-style: solid;
            border-right-style: solid;
            border-top-style: solid;
            color: $tb-iris-primary-blue;
        
}
    .k-window .k-window-titlebar .k-window-title{
    filter: blur(0px);
        
}
    .k-window{
    box-shadow: 0 16px 18px 0 rgba(0, 0, 0, 0.28),0 4px 16px 0 rgba(0, 0, 0, 0.12);
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            background-color: initial;
            background-image: none;
        
}
    .k-menu-button{
    margin-bottom: 1px;
            margin-left: 1px;
            margin-right: 1px;
            margin-top: 1px;
        
}
    .k-split-button.k-button-group .k-button.k-icon-button.k-split-button-arrow .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down::before{
    content: "\e00d";
        
}
    .k-split-button.k-button-group .k-button.k-icon-button.k-split-button-arrow .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down{
                    @extend %tb-typography-Iris-IFM-font-icons;
      
}
    .k-checkbox-wrap .k-checkbox{
    border-bottom-color: $tb-iris-primary-blue;
            border-left-color: $tb-iris-primary-blue;
            border-right-color: $tb-iris-primary-blue;
            border-top-color: $tb-iris-primary-blue;
        
}
    .k-column-menu.k-grid-columnmenu-popup.k-popup .k-columnmenu-item-wrapper .k-columnmenu-item .k-icon.k-font-icon.k-i-filter::before{
    content: "\e0cb";
        
}
    .k-column-menu.k-grid-columnmenu-popup.k-popup .k-columnmenu-item-wrapper .k-columnmenu-item .k-icon.k-font-icon.k-i-filter{
                    @extend %tb-typography-Iris-IFM-font-icons;
      
}
    .k-button-md.k-rounded-md.k-button-flat.k-button-flat-base.k-button.k-selected{
    border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
        
}
    .k-combobox.k-hover .k-input-button.k-hover,.k-combobox.k-hover .k-input-button.k-state-hover,.k-combobox.k-hover .k-input-button.k-state-hovered,.k-combobox.k-hover .k-input-button:hover,.k-combobox.k-state-hover .k-input-button.k-hover,.k-combobox.k-state-hover .k-input-button.k-state-hover,.k-combobox.k-state-hover .k-input-button.k-state-hovered,.k-combobox.k-state-hover .k-input-button:hover,.k-combobox.k-state-hovered .k-input-button.k-hover,.k-combobox.k-state-hovered .k-input-button.k-state-hover,.k-combobox.k-state-hovered .k-input-button.k-state-hovered,.k-combobox.k-state-hovered .k-input-button:hover,.k-combobox:hover .k-input-button.k-hover,.k-combobox:hover .k-input-button.k-state-hover,.k-combobox:hover .k-input-button.k-state-hovered,.k-combobox:hover .k-input-button:hover{
    width: $tb-iris-form-input-height;
            height: $tb-iris-form-input-height;
            border-bottom-left-radius: $tb-iris-border-radius;
            border-top-left-radius: $tb-iris-border-radius;
        
}
    .k-combobox.k-hover .k-input-button.k-hover,.k-combobox.k-hover  .k-input-button.k-state-hover,.k-combobox.k-hover  .k-input-button.k-state-hovered,.k-combobox.k-hover  .k-input-button:hover, .k-combobox.k-state-hover .k-input-button.k-hover, .k-combobox.k-state-hover  .k-input-button.k-state-hover, .k-combobox.k-state-hover  .k-input-button.k-state-hovered, .k-combobox.k-state-hover  .k-input-button:hover, .k-combobox.k-state-hovered .k-input-button.k-hover, .k-combobox.k-state-hovered  .k-input-button.k-state-hover, .k-combobox.k-state-hovered  .k-input-button.k-state-hovered, .k-combobox.k-state-hovered  .k-input-button:hover, .k-combobox:hover .k-input-button.k-hover, .k-combobox:hover  .k-input-button.k-state-hover, .k-combobox:hover  .k-input-button.k-state-hovered, .k-combobox:hover  .k-input-button:hover{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-split-button.k-button-group.k-focus .k-button.k-first-child,.k-split-button.k-button-group.k-focus .k-button:first-child,.k-split-button.k-button-group.k-state-focus .k-button.k-first-child,.k-split-button.k-button-group.k-state-focus .k-button:first-child,.k-split-button.k-button-group.k-state-focused .k-button.k-first-child,.k-split-button.k-button-group.k-state-focused .k-button:first-child,.k-split-button.k-button-group:focus .k-button.k-first-child,.k-split-button.k-button-group:focus .k-button:first-child{
    color: $tb-iris-primary-blue-dark-30;
            border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
        
}
    .k-split-button.k-button-group.k-focus .k-button.k-icon-button.k-split-button-arrow, .k-split-button.k-button-group.k-state-focus .k-button.k-icon-button.k-split-button-arrow, .k-split-button.k-button-group.k-state-focused .k-button.k-icon-button.k-split-button-arrow, .k-split-button.k-button-group:focus .k-button.k-icon-button.k-split-button-arrow{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
      
}
    .k-split-button.k-button-group.k-focus, .k-split-button.k-button-group.k-state-focus, .k-split-button.k-button-group.k-state-focused, .k-split-button.k-button-group:focus{
    box-shadow: none;
        
}
    .k-dropdownlist.k-picker.k-picker-md.k-rounded-md.k-picker-solid .k-input-button.k-hover,.k-dropdownlist.k-picker.k-picker-md.k-rounded-md.k-picker-solid  .k-input-button.k-state-hover,.k-dropdownlist.k-picker.k-picker-md.k-rounded-md.k-picker-solid  .k-input-button.k-state-hovered,.k-dropdownlist.k-picker.k-picker-md.k-rounded-md.k-picker-solid  .k-input-button:hover{
                    @extend %tb-effects-tb-internal-none-effects;
          border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            border-bottom-color: transparent;
            border-left-color: transparent;
            border-right-color: transparent;
            border-top-color: transparent;
            background-color: transparent;
            background-image: none;
        
}
    .k-dropdownlist.k-invalid.k-focus,.k-dropdownlist.ng-invalid.ng-touched.k-focus,.k-dropdownlist.ng-invalid.ng-dirty.k-focus,.k-dropdownlist.k-invalid.k-state-focus,.k-dropdownlist.k-invalid.k-state-focused,.k-dropdownlist.k-invalid:focus,.k-dropdownlist.ng-invalid.ng-touched.k-state-focus,.k-dropdownlist.ng-invalid.ng-touched.k-state-focused,.k-dropdownlist.ng-invalid.ng-touched:focus,.k-dropdownlist.ng-invalid.ng-dirty.k-state-focus,.k-dropdownlist.ng-invalid.ng-dirty.k-state-focused,.k-dropdownlist.ng-invalid.ng-dirty:focus{
    border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
            border-bottom-color: transparent;
            border-left-color: transparent;
            border-right-color: transparent;
            border-top-color: transparent;
        
}
    .k-dropdowntree.k-hover.k-picker, .k-dropdowntree.k-state-hover.k-picker, .k-dropdowntree.k-state-hovered.k-picker, .k-dropdowntree:hover.k-picker{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
      
}
    .k-dropdowntree.k-focus.k-picker-solid, .k-dropdowntree.k-state-focus.k-picker-solid, .k-dropdowntree.k-state-focused.k-picker-solid, .k-dropdowntree:focus.k-picker-solid{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
      
}
    .k-dropdowntree.k-invalid,.k-dropdowntree.ng-invalid.ng-touched,.k-dropdowntree.ng-invalid.ng-dirty{
    border-bottom-color: $tb-invalid;
            border-left-color: $tb-invalid;
            border-right-color: $tb-invalid;
            border-top-color: $tb-invalid;
            color: $tb-invalid;
        
}
    .k-dropdowntree.k-invalid.k-picker, .k-dropdowntree.ng-invalid.ng-touched.k-picker, .k-dropdowntree.ng-invalid.ng-dirty.k-picker{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-dropdowntree.k-invalid.k-focus.k-picker-solid, .k-dropdowntree.ng-invalid.ng-touched.k-focus.k-picker-solid, .k-dropdowntree.ng-invalid.ng-dirty.k-focus.k-picker-solid, .k-dropdowntree.k-invalid.k-state-focus.k-picker-solid, .k-dropdowntree.k-invalid.k-state-focused.k-picker-solid, .k-dropdowntree.k-invalid:focus.k-picker-solid, .k-dropdowntree.ng-invalid.ng-touched.k-state-focus.k-picker-solid, .k-dropdowntree.ng-invalid.ng-touched.k-state-focused.k-picker-solid, .k-dropdowntree.ng-invalid.ng-touched:focus.k-picker-solid, .k-dropdowntree.ng-invalid.ng-dirty.k-state-focus.k-picker-solid, .k-dropdowntree.ng-invalid.ng-dirty.k-state-focused.k-picker-solid, .k-dropdowntree.ng-invalid.ng-dirty:focus.k-picker-solid{
                    @extend %tb-effects-iris-invalid-select-inner-shadow;
      
}
    .k-dropdowntree.k-disabled,.k-dropdowntree.k-state-disabled,.k-dropdowntree:disabled{
    border-bottom-color: $tb-disabled-border;
            border-left-color: $tb-disabled-border;
            border-right-color: $tb-disabled-border;
            border-top-color: $tb-disabled-border;
            border-bottom-style: dashed;
            border-left-style: dashed;
            border-right-style: dashed;
            border-top-style: dashed;
            color: $tb-disabled-text;
        
}
    .k-dropdowntree.k-invalid .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.ng-invalid.ng-touched .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.ng-invalid.ng-dirty .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down{
    color: $tb-iris-primary-blue;
        
}
    .k-dropdowntree.k-invalid.k-focus .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.ng-invalid.ng-touched.k-focus .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.ng-invalid.ng-dirty.k-focus .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.k-invalid.k-state-focus .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.k-invalid.k-state-focused .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.k-invalid:focus .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.ng-invalid.ng-touched.k-state-focus .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.ng-invalid.ng-touched.k-state-focused .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.ng-invalid.ng-touched:focus .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.ng-invalid.ng-dirty.k-state-focus .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.ng-invalid.ng-dirty.k-state-focused .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-dropdowntree.ng-invalid.ng-dirty:focus .k-input-button.k-button.k-icon-button .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down{
    color: $tb-iris-primary-blue-dark-30;
        
}
    .k-combobox.k-dropdowngrid.k-input .k-input-button{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-combobox.k-dropdowngrid.k-hover .k-input-button.k-hover,.k-combobox.k-dropdowngrid.k-hover .k-input-button.k-state-hover,.k-combobox.k-dropdowngrid.k-hover .k-input-button.k-state-hovered,.k-combobox.k-dropdowngrid.k-hover .k-input-button:hover,.k-combobox.k-dropdowngrid.k-state-hover .k-input-button.k-hover,.k-combobox.k-dropdowngrid.k-state-hover .k-input-button.k-state-hover,.k-combobox.k-dropdowngrid.k-state-hover .k-input-button.k-state-hovered,.k-combobox.k-dropdowngrid.k-state-hover .k-input-button:hover,.k-combobox.k-dropdowngrid.k-state-hovered .k-input-button.k-hover,.k-combobox.k-dropdowngrid.k-state-hovered .k-input-button.k-state-hover,.k-combobox.k-dropdowngrid.k-state-hovered .k-input-button.k-state-hovered,.k-combobox.k-dropdowngrid.k-state-hovered .k-input-button:hover,.k-combobox.k-dropdowngrid:hover .k-input-button.k-hover,.k-combobox.k-dropdowngrid:hover .k-input-button.k-state-hover,.k-combobox.k-dropdowngrid:hover .k-input-button.k-state-hovered,.k-combobox.k-dropdowngrid:hover .k-input-button:hover{
    width: $tb-iris-form-input-height;
            height: $tb-iris-form-input-height;
            color: $tb-iris-primary-blue-dark-30;
        
}
    .k-combobox.k-input.k-hover .k-clear-value.k-hover .k-icon.k-font-icon.k-i-x,.k-combobox.k-input.k-hover  .k-clear-value.k-state-hover .k-icon.k-font-icon.k-i-x,.k-combobox.k-input.k-hover  .k-clear-value.k-state-hovered .k-icon.k-font-icon.k-i-x,.k-combobox.k-input.k-hover  .k-clear-value:hover .k-icon.k-font-icon.k-i-x, .k-combobox.k-input.k-state-hover .k-clear-value.k-hover .k-icon.k-font-icon.k-i-x, .k-combobox.k-input.k-state-hover  .k-clear-value.k-state-hover .k-icon.k-font-icon.k-i-x, .k-combobox.k-input.k-state-hover  .k-clear-value.k-state-hovered .k-icon.k-font-icon.k-i-x, .k-combobox.k-input.k-state-hover  .k-clear-value:hover .k-icon.k-font-icon.k-i-x, .k-combobox.k-input.k-state-hovered .k-clear-value.k-hover .k-icon.k-font-icon.k-i-x, .k-combobox.k-input.k-state-hovered  .k-clear-value.k-state-hover .k-icon.k-font-icon.k-i-x, .k-combobox.k-input.k-state-hovered  .k-clear-value.k-state-hovered .k-icon.k-font-icon.k-i-x, .k-combobox.k-input.k-state-hovered  .k-clear-value:hover .k-icon.k-font-icon.k-i-x, .k-combobox.k-input:hover .k-clear-value.k-hover .k-icon.k-font-icon.k-i-x, .k-combobox.k-input:hover  .k-clear-value.k-state-hover .k-icon.k-font-icon.k-i-x, .k-combobox.k-input:hover  .k-clear-value.k-state-hovered .k-icon.k-font-icon.k-i-x, .k-combobox.k-input:hover  .k-clear-value:hover .k-icon.k-font-icon.k-i-x{
    color: $tb-iris-primary-blue-dark-30;
        
}
    .k-combobox.k-input.k-hover, .k-combobox.k-input.k-state-hover, .k-combobox.k-input.k-state-hovered, .k-combobox.k-input:hover{
                    @extend %tb-effects-iris-standard-select-inner-shadow;
          border-bottom-color: $tb-iris-primary-blue-dark-30;
            border-left-color: $tb-iris-primary-blue-dark-30;
            border-right-color: $tb-iris-primary-blue-dark-30;
            border-top-color: $tb-iris-primary-blue-dark-30;
        
}
    .k-combobox.k-input.k-invalid.k-state-focus-within.k-input-solid, .k-combobox.k-input.ng-invalid.ng-touched.k-state-focus-within.k-input-solid, .k-combobox.k-input.ng-invalid.ng-dirty.k-state-focus-within.k-input-solid, .k-combobox.k-input.k-invalid:focus-within.k-input-solid, .k-combobox.k-input.ng-invalid.ng-touched:focus-within.k-input-solid, .k-combobox.k-input.ng-invalid.ng-dirty:focus-within.k-input-solid{
                    @extend %tb-effects-iris-invalid-select-inner-shadow;
      
}
    .k-combobox.k-dropdowngrid.k-input{
    margin-bottom: 0px;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 0px;
            border-bottom-color: $tb-iris-standard-border;
            border-left-color: $tb-iris-standard-border;
            border-right-color: $tb-iris-standard-border;
            border-top-color: $tb-iris-standard-border;
        
}
    .k-combobox.k-dropdowngrid.k-input.k-input-solid{
    color: $tb-kendo-base-text;
        
}
    .k-combobox.k-dropdowngrid.k-input.k-hover, .k-combobox.k-dropdowngrid.k-input.k-state-hover, .k-combobox.k-dropdowngrid.k-input.k-state-hovered, .k-combobox.k-dropdowngrid.k-input:hover{
    border-bottom-width: $tb-iris-standard-border-width;
            border-left-width: $tb-iris-standard-border-width;
            border-right-width: $tb-iris-standard-border-width;
            border-top-width: $tb-iris-standard-border-width;
        
}
    .k-datetimepicker.k-input.k-disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button, .k-datetimepicker.k-input.k-state-disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button, .k-datetimepicker.k-input:disabled .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button{
    color: $tb-disabled-text;
            background-color: $tb-disabled-background;
            background-image: none;
        
}
    .k-combobox.k-input .k-clear-value{
    color: $tb-iris-neutral-grey-70;
            border-bottom-style: none;
            border-left-style: none;
            border-right-style: none;
            border-top-style: none;
        
}
    .k-combobox.k-hover .k-input-button.k-hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down,.k-combobox.k-hover  .k-input-button.k-state-hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down,.k-combobox.k-hover  .k-input-button.k-state-hovered .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down,.k-combobox.k-hover  .k-input-button:hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox.k-state-hover .k-input-button.k-hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox.k-state-hover  .k-input-button.k-state-hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox.k-state-hover  .k-input-button.k-state-hovered .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox.k-state-hover  .k-input-button:hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox.k-state-hovered .k-input-button.k-hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox.k-state-hovered  .k-input-button.k-state-hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox.k-state-hovered  .k-input-button.k-state-hovered .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox.k-state-hovered  .k-input-button:hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox:hover .k-input-button.k-hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox:hover  .k-input-button.k-state-hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox:hover  .k-input-button.k-state-hovered .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down, .k-combobox:hover  .k-input-button:hover .k-button-icon.k-icon.k-font-icon.k-i-caret-alt-down{
    color: $tb-iris-primary-blue-dark-30;
        
}
    .k-expander.k-focus.k-expanded, .k-expander.k-state-focus.k-expanded, .k-expander.k-state-focused.k-expanded, .k-expander:focus.k-expanded{
    box-shadow: none;
        
}
    .k-expander.k-expanded{
    border-bottom-color: $tb-iris-standard-border;
            border-left-color: $tb-iris-standard-border;
            border-right-color: $tb-iris-standard-border;
            border-top-color: $tb-iris-standard-border;
            border-top-style: none;
            border-top-width: 0px;
            border-right-style: none;
            border-right-width: 0px;
            border-left-style: none;
            border-left-width: 0px;
        
}
    .k-expander.k-disabled .k-expander-header, .k-expander.k-state-disabled .k-expander-header, .k-expander:disabled .k-expander-header{
    background-color: $tb-disabled-background;
            background-image: none;
            color: $tb-kendo-disabled-text;
        
}
    .k-expander.k-disabled .k-expander-header .k-expander-title, .k-expander.k-state-disabled .k-expander-header .k-expander-title, .k-expander:disabled .k-expander-header .k-expander-title{
    color: $tb-disabled-text;
        
}
    .k-expander .k-expander-header .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-down{
    color: $tb-iris-primary-blue;
        
}
    .k-expander .k-expander-header.k-hover .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-down,.k-expander  .k-expander-header.k-state-hover .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-down,.k-expander  .k-expander-header.k-state-hovered .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-down,.k-expander  .k-expander-header:hover .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-down{
    color: $tb-iris-primary-blue-dark-30;
        
}
    .k-expander.k-disabled .k-expander-header .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-down, .k-expander.k-state-disabled .k-expander-header .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-down, .k-expander:disabled .k-expander-header .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-down{
    color: $tb-disabled-text;
        
}
    .k-expander.k-expanded .k-expander-header .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-up{
    color: $tb-iris-primary-blue;
        
}
    .k-expander.k-expanded .k-expander-header.k-hover .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-up,.k-expander.k-expanded  .k-expander-header.k-state-hover .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-up,.k-expander.k-expanded  .k-expander-header.k-state-hovered .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-up,.k-expander.k-expanded  .k-expander-header:hover .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-up{
    color: $tb-iris-primary-blue-dark-30;
        
}
    .k-expander.k-expanded.k-disabled .k-expander-header .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-up, .k-expander.k-expanded.k-state-disabled .k-expander-header .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-up, .k-expander.k-expanded:disabled .k-expander-header .k-expander-indicator .k-icon.k-font-icon.k-i-chevron-up{
    color: $tb-disabled-text;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row,.k-grid.k-grid-md .k-grid-header .k-table-row{
    align-self: center;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row .k-header.k-table-th .k-cell-inner .k-link .k-column-title,.k-grid.k-grid-md .k-grid-header .k-table-row .k-header.k-table-th .k-cell-inner .k-link .k-column-title{
    align-self: center;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row .k-header.k-table-th.k-focus,.k-grid.k-grid-md .k-table-thead .k-table-row  .k-header.k-table-th.k-state-focus,.k-grid.k-grid-md .k-table-thead .k-table-row  .k-header.k-table-th.k-state-focused,.k-grid.k-grid-md .k-table-thead .k-table-row  .k-header.k-table-th:focus,.k-grid.k-grid-md  .k-grid-header .k-table-row .k-header.k-table-th.k-focus,.k-grid.k-grid-md  .k-grid-header .k-table-row  .k-header.k-table-th.k-state-focus,.k-grid.k-grid-md  .k-grid-header .k-table-row  .k-header.k-table-th.k-state-focused,.k-grid.k-grid-md  .k-grid-header .k-table-row  .k-header.k-table-th:focus{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-grid.k-grid-md .k-table-thead .k-table-row .k-header.k-table-th.k-filterable.k-focus,.k-grid.k-grid-md .k-table-thead .k-table-row .k-header.k-table-th.k-filterable.k-state-focus,.k-grid.k-grid-md .k-table-thead .k-table-row .k-header.k-table-th.k-filterable.k-state-focused,.k-grid.k-grid-md .k-table-thead .k-table-row .k-header.k-table-th.k-filterable:focus,.k-grid.k-grid-md .k-grid-header .k-table-row .k-header.k-table-th.k-filterable.k-focus,.k-grid.k-grid-md .k-grid-header .k-table-row .k-header.k-table-th.k-filterable.k-state-focus,.k-grid.k-grid-md .k-grid-header .k-table-row .k-header.k-table-th.k-filterable.k-state-focused,.k-grid.k-grid-md .k-grid-header .k-table-row .k-header.k-table-th.k-filterable:focus{
    background-color: $tb-iris-secondary-blue-10;
            background-image: none;
        
}
    .k-grid.k-grid-md .k-table-thead .k-table-row .k-header.k-table-th.k-filterable.k-focus,.k-grid.k-grid-md .k-table-thead .k-table-row  .k-header.k-table-th.k-filterable.k-state-focus,.k-grid.k-grid-md .k-table-thead .k-table-row  .k-header.k-table-th.k-filterable.k-state-focused,.k-grid.k-grid-md .k-table-thead .k-table-row  .k-header.k-table-th.k-filterable:focus,.k-grid.k-grid-md  .k-grid-header .k-table-row .k-header.k-table-th.k-filterable.k-focus,.k-grid.k-grid-md  .k-grid-header .k-table-row  .k-header.k-table-th.k-filterable.k-state-focus,.k-grid.k-grid-md  .k-grid-header .k-table-row  .k-header.k-table-th.k-filterable.k-state-focused,.k-grid.k-grid-md  .k-grid-header .k-table-row  .k-header.k-table-th.k-filterable:focus{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-grid.k-grid-md .k-table-thead .k-table-row .k-header.k-table-th.k-focus .k-cell-inner .k-link .k-column-title,.k-grid.k-grid-md .k-table-thead .k-table-row  .k-header.k-table-th.k-state-focus .k-cell-inner .k-link .k-column-title,.k-grid.k-grid-md .k-table-thead .k-table-row  .k-header.k-table-th.k-state-focused .k-cell-inner .k-link .k-column-title,.k-grid.k-grid-md .k-table-thead .k-table-row  .k-header.k-table-th:focus .k-cell-inner .k-link .k-column-title,.k-grid.k-grid-md  .k-grid-header .k-table-row .k-header.k-table-th.k-focus .k-cell-inner .k-link .k-column-title,.k-grid.k-grid-md  .k-grid-header .k-table-row  .k-header.k-table-th.k-state-focus .k-cell-inner .k-link .k-column-title,.k-grid.k-grid-md  .k-grid-header .k-table-row  .k-header.k-table-th.k-state-focused .k-cell-inner .k-link .k-column-title,.k-grid.k-grid-md  .k-grid-header .k-table-row  .k-header.k-table-th:focus .k-cell-inner .k-link .k-column-title{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-grid.k-grid-md .k-table-thead .k-table-row .k-header.k-table-th.k-focus .k-cell-inner .k-link,.k-grid.k-grid-md .k-table-thead .k-table-row  .k-header.k-table-th.k-state-focus .k-cell-inner .k-link,.k-grid.k-grid-md .k-table-thead .k-table-row  .k-header.k-table-th.k-state-focused .k-cell-inner .k-link,.k-grid.k-grid-md .k-table-thead .k-table-row  .k-header.k-table-th:focus .k-cell-inner .k-link,.k-grid.k-grid-md  .k-grid-header .k-table-row .k-header.k-table-th.k-focus .k-cell-inner .k-link,.k-grid.k-grid-md  .k-grid-header .k-table-row  .k-header.k-table-th.k-state-focus .k-cell-inner .k-link,.k-grid.k-grid-md  .k-grid-header .k-table-row  .k-header.k-table-th.k-state-focused .k-cell-inner .k-link,.k-grid.k-grid-md  .k-grid-header .k-table-row  .k-header.k-table-th:focus .k-cell-inner .k-link{
                    @extend %tb-effects-tb-internal-none-effects;
      
}
    .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-active .k-button-text,.k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-state-active .k-button-text,.k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button:active .k-button-text{
    color: $tb-iris-neutral-light-bg;
        
}
    .k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-active .k-button-icon.k-icon.k-font-icon,.k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button.k-state-active .k-button-icon.k-icon.k-font-icon,.k-button-md.k-rounded-md.k-button-outline.k-button-outline-base.k-button:active .k-button-icon.k-icon.k-font-icon{
    color: k-color( on-dark );
        
}