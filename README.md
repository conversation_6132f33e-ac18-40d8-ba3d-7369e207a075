# DMS Portal Manager App

[![Vite](https://img.shields.io/badge/Vite-4.0+-blueviolet?logo=vite&logoColor=white)](https://vitejs.dev/)
[![React](https://img.shields.io/badge/React-18.0+-61DAFB?logo=react&logoColor=white)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-4.0+-3178C6?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Redux Toolkit](https://img.shields.io/badge/Redux%20Toolkit-1.9+-764ABC?logo=redux&logoColor=white)](https://redux-toolkit.js.org/)
[![KendoReact](https://img.shields.io/badge/KendoReact-Professional%20UI%20Components-ff6b00?logo=kendo-ui&logoColor=white)](https://www.telerik.com/kendo-react-ui/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

---

## 🚀 Project Features

- ⚡ Fast development with [Vite](https://vitejs.dev/)
- 💡 Type-safe codebase with [TypeScript](https://www.typescriptlang.org/)
- 🛠️ State management using [Redux Toolkit](https://redux-toolkit.js.org/)
- 🎨 Professional UI with [KendoReact](https://www.telerik.com/kendo-react-ui/)
- 🔐 Secure authentication via [Okta](https://developer.okta.com/)
- 🌍 Multi-language support with `react-i18next`
- 📦 Scalable, component-driven architecture

---

## 📁 Folder Structure

```
src/
├── api/                # Centralized API call logic
├── assets/             # Static assets (images, fonts, etc.)
├── components/         # Reusable components (layout, UI elements)
├── config/             # Environment-based app configurations
├── constants/          # Application constants (routes, enums, etc.)
├── features/           # Feature-based modules (auth, dashboard, etc.)
├── hooks/              # Custom React hooks
├── layouts/            # Layout components (DashboardLayout, UnauthorizedLayout)
├── routes/             # App routes with authentication guards
├── store/              # Redux store setup (slices, middleware)
├── utils/              # Utility functions (logger, helpers)
├── wrappers/           # Higher Order Components (RequireAuth, etc.)
├── App.tsx             # App root component
├── main.tsx            # Vite main entry point
└── index.ts            # App initialization
```

---

## 🔧 Getting Started

### 1. Node.js Version

Make sure you have **Node.js >= 18.x** and **npm >= 9.x** installed.

You can check your Node.js version by running:

```bash
node -v
npm -v
```

> Recommended: Use [nvm](https://github.com/nvm-sh/nvm) to manage Node.js versions.

### 2. Clone the repository

```bash
git clone https://<EMAIL>/iris-global/Document%20Management/_git/FE-DMS-Portal-Manager
cd FE-DMS-Portal-Manager
```

### 3. Install dependencies

```bash
npm install
```

### 4. Setup environment variables

Create `.env.development` and `.env.production`:

```
VITE_OKTA_CLIENT_ID=your-client-id
VITE_OKTA_ISSUER=https://your-okta-domain/oauth2/default
VITE_API_BASE_URL=https://your-api-domain.com/api
VITE_DEFAULT_LANGUAGE=en

```

> **Note**: Vite requires environment variables to be prefixed with `VITE_`.

### 5. Start the development server

```bash
npm run dev
```

The app will be running at [http://localhost:3000](http://localhost:3000)

---

## 🧪 Running Tests

```bash
npx vitest run
```

## 🧮 Running Test Coverage

```bash
npx vitest run --coverage
```
```bash
open coverage/index.html       # macOS
```
```bash
xdg-open coverage/index.html   # Linux
```
```bash
start coverage/index.html      # Windows
```
---

## 🚀 Build for Production

```bash
npm run build
```

## 📄 License

This project is licensed under the [MIT License](LICENSE).

---

## 🤝 Contribution Guidelines

1. Fork the repository.
2. Create a new feature branch: `git checkout -b feature/<ticket-id>-<feature-name>`.
3. Create a new bugfix branch: `git checkout -b bugfix/<bug-id>-<bug-description>`.
4. Commit your changes: `git commit -m 'Add YourFeature'`.
5. Push to your branch: `git push origin feature/YourFeature`.
6. Open a Pull Request.

---

## 🙏 Acknowledgements

- [React](https://reactjs.org/)
- [Vite](https://vitejs.dev/)
- [Redux Toolkit](https://redux-toolkit.js.org/)
- [KendoReact](https://www.telerik.com/kendo-react-ui/)
- [Okta](https://developer.okta.com/)
- [react-i18next](https://react.i18next.com/)