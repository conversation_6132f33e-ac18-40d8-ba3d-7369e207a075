import { Button } from "@progress/kendo-react-buttons";
import "./AlertBox.scss";

interface AlertBoxProps {
  message: string;
  onClose: () => void;
}

export default function AlertBox({ message, onClose }: AlertBoxProps) {
  return (
    <div className="alert-box">
      <div className="alert-icon">i</div>
      <div className="alert-message">{message}</div>
      <Button icon="close" fillMode="flat" onClick={onClose} />
    </div>
  );
}
